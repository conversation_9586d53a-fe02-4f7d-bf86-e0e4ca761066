{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Downloads/Work __Abder<PERSON>mane_ouhna/OCR_DOCUMENT_GROSSISTE/Frontend ocr grossiste document/frontend_ocr_grossiste_document/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nvar _LoginPage;\nimport { environment } from '../../environments/environment';\nimport { Keyboard } from '@capacitor/keyboard';\nimport { retry, catchError } from 'rxjs/operators';\nimport { of } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ionic/angular\";\nimport * as i2 from \"../services/api.service\";\nimport * as i3 from \"../services/storage.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nconst _c0 = a0 => ({\n  \"active\": a0\n});\nfunction LoginPage_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"div\", 9)(2, \"div\", 10)(3, \"span\", 11);\n    i0.ɵɵtext(4, \"1\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 12);\n    i0.ɵɵtext(6, \"Pharmacie\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(7, \"div\", 13);\n    i0.ɵɵelementStart(8, \"div\", 10)(9, \"span\", 11);\n    i0.ɵɵtext(10, \"2\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\", 12);\n    i0.ɵɵtext(12, \"Utilisateur\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(13, \"div\", 14)(14, \"h1\");\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"p\");\n    i0.ɵɵtext(17, \"Vous devez utiliser \");\n    i0.ɵɵelement(18, \"br\");\n    i0.ɵɵtext(19, \"les identifiants de WinPlusPharma\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(3, _c0, ctx_r0.showTenantLogin));\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(5, _c0, !ctx_r0.showTenantLogin));\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r0.showTenantLogin ? \"Connexion de la pharmacie\" : \"Connexion de l'utilisateur\");\n  }\n}\nfunction LoginPage_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"div\", 14)(2, \"h1\");\n    i0.ɵɵtext(3, \"Connexion Pharmalien\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\");\n    i0.ɵɵtext(5, \"Vous devez utiliser \");\n    i0.ɵɵelement(6, \"br\");\n    i0.ɵɵtext(7, \"les identifiants de Pharmalien\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction LoginPage_div_7_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 17)(1, \"ion-item\", 18)(2, \"ion-input\", 19);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function LoginPage_div_7_div_1_Template_ion_input_ngModelChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r0.tenantUsername, $event) || (ctx_r0.tenantUsername = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"ion-item\", 18)(4, \"ion-input\", 20);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function LoginPage_div_7_div_1_Template_ion_input_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r0.tenantPassword, $event) || (ctx_r0.tenantPassword = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"ion-button\", 21);\n    i0.ɵɵlistener(\"click\", function LoginPage_div_7_div_1_Template_ion_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.tenantLogin());\n    });\n    i0.ɵɵtext(6, \"Se connecter\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r0.tenantUsername);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r0.tenantPassword);\n  }\n}\nfunction LoginPage_div_7_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 17)(1, \"ion-item\", 18)(2, \"ion-input\", 22);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function LoginPage_div_7_div_2_Template_ion_input_ngModelChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r0.username, $event) || (ctx_r0.username = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"ion-item\", 18)(4, \"ion-input\", 23);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function LoginPage_div_7_div_2_Template_ion_input_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r0.password, $event) || (ctx_r0.password = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"ion-button\", 21);\n    i0.ɵɵlistener(\"click\", function LoginPage_div_7_div_2_Template_ion_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.userLogin());\n    });\n    i0.ɵɵtext(6, \"Se connecter\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r0.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r0.password);\n  }\n}\nfunction LoginPage_div_7_ion_button_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ion-button\", 4);\n    i0.ɵɵlistener(\"click\", function LoginPage_div_7_ion_button_3_Template_ion_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.goBackToTenantLogin());\n    });\n    i0.ɵɵelement(1, \"ion-icon\", 5);\n    i0.ɵɵtext(2, \" Retour \\u00E0 la connexion de la pharmacie \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginPage_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8);\n    i0.ɵɵtemplate(1, LoginPage_div_7_div_1_Template, 7, 2, \"div\", 15)(2, LoginPage_div_7_div_2_Template, 7, 2, \"div\", 15)(3, LoginPage_div_7_ion_button_3_Template, 3, 0, \"ion-button\", 16);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.showTenantLogin);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.showTenantLogin);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.showTenantLogin);\n  }\n}\nfunction LoginPage_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"div\", 17)(2, \"ion-item\", 18)(3, \"ion-input\", 24);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function LoginPage_div_8_Template_ion_input_ngModelChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r0 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r0.username, $event) || (ctx_r0.username = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"ion-item\", 18)(5, \"ion-input\", 25);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function LoginPage_div_8_Template_ion_input_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r0 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r0.password, $event) || (ctx_r0.password = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"ion-button\", 21);\n    i0.ɵɵlistener(\"click\", function LoginPage_div_8_Template_ion_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.pharmalienLogin());\n    });\n    i0.ɵɵtext(7, \"Se connecter\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r0.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r0.password);\n  }\n}\nexport class LoginPage {\n  constructor(navCtrl, apiService, toastController, loadingController, storageService) {\n    this.navCtrl = navCtrl;\n    this.apiService = apiService;\n    this.toastController = toastController;\n    this.loadingController = loadingController;\n    this.storageService = storageService;\n    this.tenantUsername = '';\n    this.tenantPassword = '';\n    this.username = '';\n    this.password = '';\n    this.showTenantLogin = true;\n    this.tenantToken = '';\n    this.hasSeenOnboarding = false;\n    this.localCredentials = {};\n    this.selectedPlatform = 'winpluspharma';\n    this.isPharmalienPlatform = false;\n  }\n  ngOnInit() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (environment.production) {\n        _this.tenantUsername = '';\n        _this.tenantPassword = '';\n        _this.username = '';\n        _this.password = '';\n      } else {\n        _this.tenantUsername = '0001';\n        _this.tenantPassword = '123456';\n        _this.username = 'PH';\n        _this.password = 'PH';\n      }\n      // Load the credentials from the local storage\n      _this.localCredentials = yield JSON.parse(localStorage.getItem('credentials'));\n      if (_this.localCredentials) {\n        _this.tenantUsername = _this.localCredentials.tenantUsername;\n        _this.tenantPassword = _this.localCredentials.tenantPassword;\n        _this.username = _this.localCredentials.username;\n        _this.password = _this.localCredentials.password;\n      }\n      Keyboard.addListener('keyboardWillShow', () => {\n        const footer = document.querySelector('ion-footer');\n        footer === null || footer === void 0 || footer.classList.add('keyboard-open');\n      });\n      Keyboard.addListener('keyboardWillHide', () => {\n        const footer = document.querySelector('ion-footer');\n        footer === null || footer === void 0 || footer.classList.remove('keyboard-open');\n      });\n      // Initialize storage\n      yield _this.storageService.init();\n      // Check if the user has seen onboarding\n      _this.hasSeenOnboarding = yield _this.storageService.get('hasSeenOnboarding');\n      // Detect selected platform\n      const platform = localStorage.getItem('src_app');\n      if (platform) {\n        _this.selectedPlatform = platform;\n        _this.isPharmalienPlatform = platform === 'pharmalier';\n        console.log('Platform detected:', platform);\n      } else {\n        // If no platform selected, redirect back to welcome\n        _this.navCtrl.navigateRoot('/welcome');\n      }\n    })();\n  }\n  ngOnDestroy() {\n    Keyboard.removeAllListeners();\n  }\n  tenantLogin() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      // Check if email and password are empty\n      if (_this2.tenantUsername === '' || _this2.tenantPassword === '') {\n        const toast = _this2.toastController.create({\n          message: 'Veuillez remplir les champs de connexion.',\n          duration: 2000,\n          color: 'danger'\n        }).then(toast => toast.present());\n        return;\n      }\n      const loading = yield _this2.loadingController.create({\n        message: 'Connexion de la pharmacie...'\n      });\n      yield loading.present();\n      const tenantRequest = {\n        username: _this2.tenantUsername,\n        password: _this2.tenantPassword\n      };\n      _this2.apiService.tenantLogin(tenantRequest).pipe(retry(1),\n      // Retry the request once if it fails\n      catchError(error => {\n        console.log('Tenant login error:', error);\n        if (error.status === 403) {\n          const toast = _this2.toastController.create({\n            message: error.error.detail,\n            duration: 2000,\n            color: 'danger'\n          }).then(toast => toast.present());\n        } else {\n          const toast = _this2.toastController.create({\n            message: 'La connexion de la pharmacie a échoué. Veuillez réessayer.',\n            duration: 2000,\n            color: 'danger'\n          }).then(toast => toast.present());\n        }\n        console.error('Tenant login error:', error);\n        return of(null); // Return an observable with null to continue the stream\n      })).subscribe( /*#__PURE__*/function () {\n        var _ref = _asyncToGenerator(function* (response) {\n          yield loading.dismiss();\n          if (response) {\n            console.log('Tenant login response:', response);\n            _this2.tenantToken = response.accessToken;\n            _this2.showTenantLogin = false;\n            const toast = yield _this2.toastController.create({\n              message: 'Pharmacie connectée avec succès !',\n              duration: 2000,\n              color: 'success'\n            });\n            toast.present();\n          }\n        });\n        return function (_x) {\n          return _ref.apply(this, arguments);\n        };\n      }());\n    })();\n  }\n  userLogin() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      // Check if email and password are empty\n      if (_this3.username === '' || _this3.password === '') {\n        const toast = _this3.toastController.create({\n          message: 'Veuillez remplir les champs de connexion.',\n          duration: 2000,\n          color: 'danger'\n        }).then(toast => toast.present());\n        return;\n      }\n      const loading = yield _this3.loadingController.create({\n        message: 'Connexion ...'\n      });\n      yield loading.present();\n      const userRequest = {\n        username: _this3.username,\n        password: _this3.password,\n        tenant_token: _this3.tenantToken // Include tenant token in the request\n      };\n      _this3.apiService.userLogin(userRequest, _this3.tenantToken).subscribe( /*#__PURE__*/function () {\n        var _ref2 = _asyncToGenerator(function* (response) {\n          var _response$user_data, _response$tenant_data, _response$local_token;\n          console.log('User login response:', response);\n          // Store the user data in local storage\n          localStorage.setItem('tokenUser', JSON.stringify((_response$user_data = response.user_data) !== null && _response$user_data !== void 0 ? _response$user_data : \"\"));\n          localStorage.setItem('tokenTenant', JSON.stringify((_response$tenant_data = response.tenant_data) !== null && _response$tenant_data !== void 0 ? _response$tenant_data : \"\"));\n          localStorage.setItem('token', (_response$local_token = response.local_token) !== null && _response$local_token !== void 0 ? _response$local_token : \"\");\n          localStorage.setItem('ocrMode', 'STANDARD');\n          // Store the credentials in local storage\n          _this3.localCredentials = {\n            tenantUsername: _this3.tenantUsername,\n            tenantPassword: _this3.tenantPassword,\n            username: _this3.username,\n            password: _this3.password\n          };\n          localStorage.setItem('credentials', JSON.stringify(_this3.localCredentials));\n          const toast = yield _this3.toastController.create({\n            message: 'Login successful!',\n            duration: 2000,\n            color: 'success'\n          });\n          toast.present();\n          yield loading.dismiss();\n          if (!_this3.hasSeenOnboarding) {\n            yield _this3.storageService.set('hasSeenOnboarding', true);\n            _this3.navCtrl.navigateRoot('/guide');\n          } else {\n            _this3.navCtrl.navigateRoot('/scan-bl');\n          }\n        });\n        return function (_x2) {\n          return _ref2.apply(this, arguments);\n        };\n      }(), /*#__PURE__*/function () {\n        var _ref3 = _asyncToGenerator(function* (error) {\n          console.error('Login error:', error);\n          // Extract the error message without \"Internal server error: 400:\"\n          let errorMessage = error.error.detail || 'Connexion échouée. Veuillez vérifier vos identifiants.';\n          // Remove \"Internal server error: 400:\" pattern from the message\n          errorMessage = errorMessage.replace(/^Internal server error: \\d+: /, '');\n          const toast = yield _this3.toastController.create({\n            message: errorMessage,\n            duration: 2000,\n            color: 'danger'\n          });\n          toast.present();\n          yield loading.dismiss();\n        });\n        return function (_x3) {\n          return _ref3.apply(this, arguments);\n        };\n      }());\n    })();\n  }\n  goBackToTenantLogin() {\n    this.showTenantLogin = true;\n    this.tenantToken = '';\n  }\n  goBackToWelcome() {\n    this.navCtrl.navigateRoot('/welcome');\n  }\n  pharmalienLogin() {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      // Check if username and password are empty\n      if (_this4.username === '' || _this4.password === '') {\n        const toast = _this4.toastController.create({\n          message: 'Veuillez remplir les champs de connexion.',\n          duration: 2000,\n          color: 'danger'\n        }).then(toast => toast.present());\n        return;\n      }\n      const loading = yield _this4.loadingController.create({\n        message: 'Connexion ...'\n      });\n      yield loading.present();\n      const pharmalienRequest = {\n        username: _this4.username,\n        password: _this4.password\n      };\n      _this4.apiService.pharmalienLogin(pharmalienRequest).subscribe( /*#__PURE__*/function () {\n        var _ref4 = _asyncToGenerator(function* (response) {\n          var _response$user_data2, _response$local_token2;\n          console.log('Pharmalien login response:', response);\n          // Store the user data in local storage for Pharmalien (single token system)\n          localStorage.setItem('tokenUser', JSON.stringify((_response$user_data2 = response.user_data) !== null && _response$user_data2 !== void 0 ? _response$user_data2 : \"\"));\n          localStorage.setItem('token', (_response$local_token2 = response.local_token) !== null && _response$local_token2 !== void 0 ? _response$local_token2 : \"\");\n          localStorage.setItem('ocrMode', 'STANDARD');\n          // Store the credentials in local storage\n          _this4.localCredentials = {\n            username: _this4.username,\n            password: _this4.password\n          };\n          localStorage.setItem('credentials', JSON.stringify(_this4.localCredentials));\n          const toast = yield _this4.toastController.create({\n            message: 'Login successful!',\n            duration: 2000,\n            color: 'success'\n          });\n          toast.present();\n          yield loading.dismiss();\n          if (!_this4.hasSeenOnboarding) {\n            yield _this4.storageService.set('hasSeenOnboarding', true);\n            _this4.navCtrl.navigateRoot('/guide');\n          } else {\n            _this4.navCtrl.navigateRoot('/scan-bl');\n          }\n        });\n        return function (_x4) {\n          return _ref4.apply(this, arguments);\n        };\n      }(), /*#__PURE__*/function () {\n        var _ref5 = _asyncToGenerator(function* (error) {\n          console.error('Pharmalien login error:', error);\n          yield loading.dismiss();\n        });\n        return function (_x5) {\n          return _ref5.apply(this, arguments);\n        };\n      }());\n    })();\n  }\n}\n_LoginPage = LoginPage;\n_LoginPage.ɵfac = function LoginPage_Factory(t) {\n  return new (t || _LoginPage)(i0.ɵɵdirectiveInject(i1.NavController), i0.ɵɵdirectiveInject(i2.ApiService), i0.ɵɵdirectiveInject(i1.ToastController), i0.ɵɵdirectiveInject(i1.LoadingController), i0.ɵɵdirectiveInject(i3.StorageService));\n};\n_LoginPage.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: _LoginPage,\n  selectors: [[\"app-login\"]],\n  decls: 19,\n  vars: 5,\n  consts: [[3, \"fullscreen\"], [1, \"login-wrapper\"], [\"size\", \"12\", 1, \"login-content\"], [\"class\", \"platform-container\", 4, \"ngIf\"], [\"fill\", \"clear\", 1, \"back-button\", 3, \"click\"], [\"name\", \"arrow-back-outline\", \"slot\", \"start\"], [\"src\", \"/assets/sophatel_logo.svg\", \"alt\", \"SOPHATEL Logo\", 1, \"logo\"], [1, \"copyright\", \"text-center\"], [1, \"platform-container\"], [1, \"step-indicators\"], [1, \"step-badge\", 3, \"ngClass\"], [1, \"step-number\"], [1, \"step-label\"], [1, \"step-line\"], [1, \"content-login-head\"], [\"class\", \"inputs-login\", 4, \"ngIf\"], [\"fill\", \"clear\", \"class\", \"back-button\", 3, \"click\", 4, \"ngIf\"], [1, \"inputs-login\"], [\"lines\", \"none\", 1, \"input-item\"], [\"type\", \"text\", \"placeholder\", \"Identifiant de la pharmacie\", \"required\", \"\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"password\", \"placeholder\", \"Mot de passe de la pharmacie\", \"required\", \"\", 3, \"ngModelChange\", \"ngModel\"], [\"expand\", \"block\", 1, \"login-button\", 3, \"click\"], [\"type\", \"text\", \"placeholder\", \"Identifiant de l'utilisateur\", \"required\", \"\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"password\", \"placeholder\", \"Mot de passe de l'utilisateur\", \"required\", \"\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"text\", \"placeholder\", \"Identifiant utilisateur\", \"required\", \"\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"password\", \"placeholder\", \"Mot de passe\", \"required\", \"\", 3, \"ngModelChange\", \"ngModel\"]],\n  template: function LoginPage_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelement(0, \"ion-header\");\n      i0.ɵɵelementStart(1, \"ion-content\", 0)(2, \"div\", 1)(3, \"ion-row\")(4, \"ion-col\", 2);\n      i0.ɵɵtemplate(5, LoginPage_div_5_Template, 20, 7, \"div\", 3)(6, LoginPage_div_6_Template, 8, 0, \"div\", 3)(7, LoginPage_div_7_Template, 4, 3, \"div\", 3)(8, LoginPage_div_8_Template, 8, 2, \"div\", 3);\n      i0.ɵɵelementStart(9, \"ion-button\", 4);\n      i0.ɵɵlistener(\"click\", function LoginPage_Template_ion_button_click_9_listener() {\n        return ctx.goBackToWelcome();\n      });\n      i0.ɵɵelement(10, \"ion-icon\", 5);\n      i0.ɵɵtext(11, \" Changer de plateforme \");\n      i0.ɵɵelementEnd()()()()();\n      i0.ɵɵelementStart(12, \"ion-footer\");\n      i0.ɵɵelement(13, \"img\", 6);\n      i0.ɵɵelementStart(14, \"p\", 7)(15, \"span\");\n      i0.ɵɵtext(16, \" Sophatel Ing\\u00E9nierie \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(17, \"br\");\n      i0.ɵɵtext(18, \"WinDoc \\u00A9 2025 - Tous droits r\\u00E9serv\\u00E9s.\");\n      i0.ɵɵelementEnd()();\n    }\n    if (rf & 2) {\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"fullscreen\", true);\n      i0.ɵɵadvance(4);\n      i0.ɵɵproperty(\"ngIf\", !ctx.isPharmalienPlatform);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", ctx.isPharmalienPlatform);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", !ctx.isPharmalienPlatform);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", ctx.isPharmalienPlatform);\n    }\n  },\n  dependencies: [i4.NgClass, i4.NgIf, i5.NgControlStatus, i5.RequiredValidator, i5.NgModel, i1.IonButton, i1.IonCol, i1.IonContent, i1.IonFooter, i1.IonHeader, i1.IonIcon, i1.IonInput, i1.IonItem, i1.IonRow, i1.TextValueAccessor],\n  styles: [\"@import url(https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap);@import url(https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0[_ngcontent-%COMP%], 200[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 300[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 400[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 500[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 600[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 700[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 800[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 900[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 100[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 200[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 300[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 400[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 500[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 600[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 700[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 800[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 900&display=swap)[_ngcontent-%COMP%];*[_ngcontent-%COMP%] {\\n  font-family: \\\"Inter\\\", sans-serif;\\n  font-optical-sizing: auto;\\n}\\n\\nion-content[_ngcontent-%COMP%]::part(scroll) {\\n  scrollbar-width: none !important;\\n  -ms-overflow-style: none !important;\\n}\\n\\n.login-wrapper[_ngcontent-%COMP%] {\\n  background: url(\\\"/assets/bg-welcome.png\\\") no-repeat center center fixed;\\n  background-size: cover;\\n  min-height: 100vh;\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: center;\\n  align-items: center;\\n  position: relative;\\n  padding: 20px;\\n  padding-bottom: 120px;\\n}\\n\\n.content-login-head[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  flex-direction: column;\\n  margin-bottom: 30px;\\n}\\n\\n.content-login-head[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  color: #1f41bb;\\n  font-size: 34px;\\n  font-weight: bold;\\n  text-shadow: 0px 0px 1.5px #1f41bb;\\n  text-align: center;\\n}\\n\\n.content-login-head[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #141414;\\n  font-size: 18px;\\n  font-weight: 600;\\n  text-align: center;\\n  text-shadow: 0px 0px 0.5px #050505;\\n}\\n\\nion-toolbar[_ngcontent-%COMP%] {\\n  --background: transparent;\\n  --ion-color-primary: #2f4fcd;\\n}\\n\\nion-button.login-button[_ngcontent-%COMP%] {\\n  --background: #1f41bb;\\n  --background-activated: #1f41bb;\\n  --border-radius: 10px;\\n  width: 100%;\\n  text-align: center;\\n  box-shadow: 0px 16px 20px rgb(203, 214, 255);\\n  color: #fff;\\n  font-size: 20px;\\n  font-weight: bold;\\n  margin-top: 10px;\\n}\\n\\nion-button[_ngcontent-%COMP%]::part(native) {\\n  --padding-top: 20px !important;\\n  --padding-bottom: 20px !important;\\n}\\n\\nion-row[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n}\\n\\n  ion-col {\\n  display: flex !important;\\n  flex-direction: column;\\n  justify-content: center;\\n  align-items: center;\\n  width: 100%;\\n  max-width: 400px;\\n}\\n\\n.inputs-login[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 20px;\\n  flex-direction: column;\\n  width: 100%;\\n  max-width: 350px;\\n  align-items: center;\\n  animation: _ngcontent-%COMP%_fadeIn 0.3s ease-in-out;\\n}\\n\\n  .login-content {\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: center;\\n  align-items: center;\\n  width: 100%;\\n  padding: 20px;\\n  gap: 20px;\\n}\\n\\n  .login-content ion-item {\\n  width: 100%;\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: #050505;\\n  text-align: left;\\n  padding: 8px 15px;\\n  border: 2px solid #1f41bb;\\n  border-radius: 10px;\\n  --background: #f1f4ff;\\n  background-color: #f1f4ff;\\n  margin: 0;\\n}\\n\\n.forgot-password[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding-right: 50px;\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: #1f41bb;\\n  text-align: right;\\n  text-decoration: none;\\n  margin-bottom: 20px;\\n}\\n\\nion-footer[_ngcontent-%COMP%] {\\n  position: fixed;\\n  bottom: 0;\\n  left: 0;\\n  width: 100%;\\n  background: rgba(255, 255, 255, 0.9);\\n  padding: 10px;\\n  z-index: 999;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  transition: transform 0.3s ease-out;\\n}\\nion-footer.keyboard-open[_ngcontent-%COMP%] {\\n  transform: translateY(100%);\\n}\\n\\n.copyright[_ngcontent-%COMP%] {\\n  color: #1f41bb;\\n  padding-top: 5px;\\n  font-weight: 500;\\n  font-size: 14px;\\n  text-shadow: 0px 0px 1px #1f41bb;\\n  text-align: center;\\n}\\n.copyright[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: bold;\\n}\\n\\n.step-indicators[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin-bottom: 30px;\\n  margin-top: 10px;\\n  width: 100%;\\n  max-width: 300px;\\n}\\n\\n.step-badge[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  position: relative;\\n  transition: all 0.3s ease;\\n}\\n.step-badge[_ngcontent-%COMP%]   .step-number[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  background-color: #f1f4ff;\\n  border: 2px solid #1f41bb;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: #1f41bb;\\n  font-weight: bold;\\n  font-size: 18px;\\n  transition: all 0.3s ease;\\n}\\n.step-badge[_ngcontent-%COMP%]   .step-label[_ngcontent-%COMP%] {\\n  margin-top: 8px;\\n  color: #1f41bb;\\n  font-size: 14px;\\n  font-weight: 500;\\n}\\n.step-badge[_ngcontent-%COMP%]:hover {\\n  cursor: pointer;\\n  transform: translateY(-2px);\\n}\\n.step-badge.active[_ngcontent-%COMP%]   .step-number[_ngcontent-%COMP%] {\\n  background-color: #1f41bb;\\n  color: white;\\n  transform: scale(1.1);\\n}\\n.step-badge.active[_ngcontent-%COMP%]   .step-label[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n}\\n\\n.step-line[_ngcontent-%COMP%] {\\n  flex: 1;\\n  height: 2px;\\n  background-color: #1f41bb;\\n  margin: 0 15px;\\n  margin-bottom: 20px;\\n  max-width: 100px;\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from {\\n    opacity: 0;\\n    transform: translateY(10px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n.platform-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  width: 100%;\\n  gap: 20px;\\n}\\n\\n.back-button[_ngcontent-%COMP%] {\\n  --color: #1f41bb;\\n  font-size: 16px;\\n  font-weight: 500;\\n  margin-top: 20px;\\n}\\n.back-button[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n});", "map": {"version": 3, "names": ["environment", "Keyboard", "retry", "catchError", "of", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction1", "_c0", "ctx_r0", "showTenant<PERSON><PERSON>in", "ɵɵtextInterpolate", "ɵɵtwoWayListener", "LoginPage_div_7_div_1_Template_ion_input_ngModelChange_2_listener", "$event", "ɵɵrestoreView", "_r2", "ɵɵnextContext", "ɵɵtwoWayBindingSet", "tenantUsername", "ɵɵresetView", "LoginPage_div_7_div_1_Template_ion_input_ngModelChange_4_listener", "tenantPassword", "ɵɵlistener", "LoginPage_div_7_div_1_Template_ion_button_click_5_listener", "tenantLogin", "ɵɵtwoWayProperty", "LoginPage_div_7_div_2_Template_ion_input_ngModelChange_2_listener", "_r3", "username", "LoginPage_div_7_div_2_Template_ion_input_ngModelChange_4_listener", "password", "LoginPage_div_7_div_2_Template_ion_button_click_5_listener", "userLogin", "LoginPage_div_7_ion_button_3_Template_ion_button_click_0_listener", "_r4", "goBackToTenantLogin", "ɵɵtemplate", "LoginPage_div_7_div_1_Template", "LoginPage_div_7_div_2_Template", "LoginPage_div_7_ion_button_3_Template", "LoginPage_div_8_Template_ion_input_ngModelChange_3_listener", "_r5", "LoginPage_div_8_Template_ion_input_ngModelChange_5_listener", "LoginPage_div_8_Template_ion_button_click_6_listener", "pharmalien<PERSON><PERSON>in", "LoginPage", "constructor", "navCtrl", "apiService", "toastController", "loadingController", "storageService", "tenantToken", "hasSeenOnboarding", "localCredentials", "selectedPlatform", "isPharmalienPlatform", "ngOnInit", "_this", "_asyncToGenerator", "production", "JSON", "parse", "localStorage", "getItem", "addListener", "footer", "document", "querySelector", "classList", "add", "remove", "init", "get", "platform", "console", "log", "navigateRoot", "ngOnDestroy", "removeAllListeners", "_this2", "toast", "create", "message", "duration", "color", "then", "present", "loading", "tenantRequest", "pipe", "error", "status", "detail", "subscribe", "_ref", "response", "dismiss", "accessToken", "_x", "apply", "arguments", "_this3", "userRequest", "tenant_token", "_ref2", "_response$user_data", "_response$tenant_data", "_response$local_token", "setItem", "stringify", "user_data", "tenant_data", "local_token", "set", "_x2", "_ref3", "errorMessage", "replace", "_x3", "goBackToWelcome", "_this4", "pharmalienRequest", "_ref4", "_response$user_data2", "_response$local_token2", "_x4", "_ref5", "_x5", "ɵɵdirectiveInject", "i1", "NavController", "i2", "ApiService", "ToastController", "LoadingController", "i3", "StorageService", "selectors", "decls", "vars", "consts", "template", "LoginPage_Template", "rf", "ctx", "LoginPage_div_5_Template", "LoginPage_div_6_Template", "LoginPage_div_7_Template", "LoginPage_div_8_Template", "LoginPage_Template_ion_button_click_9_listener"], "sources": ["C:\\Users\\<USER>\\Downloads\\<PERSON> __<PERSON><PERSON><PERSON><PERSON><PERSON>_ouhna\\OCR_DOCUMENT_GROSSISTE\\Frontend ocr grossiste document\\frontend_ocr_grossiste_document\\src\\app\\login\\login.page.ts", "C:\\Users\\<USER>\\Downloads\\<PERSON> __<PERSON><PERSON><PERSON><PERSON><PERSON>_ouhna\\OCR_DOCUMENT_GROSSISTE\\Frontend ocr grossiste document\\frontend_ocr_grossiste_document\\src\\app\\login\\login.page.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { NavController, ToastController, LoadingController  } from '@ionic/angular';\r\nimport { ApiService } from '../services/api.service';\r\nimport { LoginRequest, LoginResponse, TenantLoginRequest, TenantLoginResponse, PharmalienLoginRequest, PharmalienLoginResponse } from '../../models/login';\r\nimport { environment } from '../../environments/environment';\r\nimport { Keyboard } from '@capacitor/keyboard';\r\nimport { retry, catchError } from 'rxjs/operators';\r\nimport { of } from 'rxjs';\r\nimport { StorageService } from '../services/storage.service';\r\n\r\n@Component({\r\n  selector: 'app-login',\r\n  templateUrl: './login.page.html',\r\n  styleUrls: ['./login.page.scss'],\r\n})\r\nexport class LoginPage implements OnInit {\r\n  tenantUsername: string = '';\r\n  tenantPassword: string = '';\r\n  username: string = '';\r\n  password: string = '';\r\n  showTenantLogin: boolean = true;\r\n  tenantToken: string = '';\r\n  hasSeenOnboarding: boolean = false;\r\n  localCredentials: any = {};\r\n  selectedPlatform: 'winpluspharma' | 'pharmalier' = 'winpluspharma';\r\n  isPharmalienPlatform = false;\r\n\r\n  constructor(\r\n    private navCtrl: NavController,\r\n    private apiService: ApiService,\r\n    private toastController: ToastController, \r\n    private loadingController: LoadingController,\r\n    private storageService: StorageService,\r\n  ) {}\r\n\r\n  async ngOnInit() {\r\n    if (environment.production) {\r\n      this.tenantUsername = '';\r\n      this.tenantPassword = '';\r\n      this.username = '';\r\n      this.password = '';\r\n    } else {\r\n      this.tenantUsername = '0001';\r\n      this.tenantPassword = '123456';\r\n      this.username = 'PH';\r\n      this.password = 'PH';\r\n    }\r\n\r\n\r\n    // Load the credentials from the local storage\r\n    this.localCredentials = await JSON.parse(localStorage.getItem('credentials') as any);  \r\n    if (this.localCredentials) {\r\n      this.tenantUsername = this.localCredentials.tenantUsername;\r\n      this.tenantPassword = this.localCredentials.tenantPassword\r\n      this.username = this.localCredentials.username;\r\n      this.password = this.localCredentials.password;\r\n    }\r\n\r\n    Keyboard.addListener('keyboardWillShow', () => {\r\n      const footer = document.querySelector('ion-footer');\r\n      footer?.classList.add('keyboard-open');\r\n    });\r\n\r\n    Keyboard.addListener('keyboardWillHide', () => {\r\n      const footer = document.querySelector('ion-footer');\r\n      footer?.classList.remove('keyboard-open');\r\n    });\r\n\r\n    // Initialize storage\r\n    await this.storageService.init();\r\n\r\n    // Check if the user has seen onboarding\r\n    this.hasSeenOnboarding = await this.storageService.get('hasSeenOnboarding');\r\n\r\n    // Detect selected platform\r\n    const platform = localStorage.getItem('src_app') as 'winpluspharma' | 'pharmalier';\r\n    if (platform) {\r\n      this.selectedPlatform = platform;\r\n      this.isPharmalienPlatform = platform === 'pharmalier';\r\n      console.log('Platform detected:', platform);\r\n    } else {\r\n      // If no platform selected, redirect back to welcome\r\n      this.navCtrl.navigateRoot('/welcome');\r\n    }\r\n  }\r\n\r\n\r\n  ngOnDestroy() {\r\n    Keyboard.removeAllListeners();\r\n  }\r\n\r\n  async tenantLogin() {\r\n\r\n    // Check if email and password are empty\r\n    if (this.tenantUsername === '' || this.tenantPassword === '') {\r\n      const toast = this.toastController.create({\r\n        message: 'Veuillez remplir les champs de connexion.',\r\n        duration: 2000,\r\n        color: 'danger',\r\n      }).then(toast => toast.present());\r\n      return;\r\n    }\r\n\r\n    const loading = await this.loadingController.create({\r\n      message: 'Connexion de la pharmacie...',\r\n    });\r\n    await loading.present();\r\n  \r\n    const tenantRequest: TenantLoginRequest = {\r\n      username: this.tenantUsername,\r\n      password: this.tenantPassword,\r\n    };\r\n  \r\n    this.apiService.tenantLogin(tenantRequest)\r\n      .pipe(\r\n        retry(1), // Retry the request once if it fails\r\n        catchError(error => {\r\n          console.log('Tenant login error:', error);\r\n          \r\n          if(error.status === 403){\r\n              const toast =  this.toastController.create({\r\n                message: error.error.detail,\r\n                duration: 2000,\r\n                color: 'danger',\r\n              }).then(toast => toast.present());\r\n          }\r\n          else{\r\n            const toast = this.toastController.create({\r\n              message: 'La connexion de la pharmacie a échoué. Veuillez réessayer.',\r\n              duration: 2000,\r\n              color: 'danger',\r\n            }).then(toast => toast.present());\r\n          }\r\n          console.error('Tenant login error:', error);\r\n          return of(null); // Return an observable with null to continue the stream\r\n        })\r\n      )\r\n      .subscribe(\r\n        async (response: TenantLoginResponse | null) => {\r\n          await loading.dismiss();\r\n          if (response) {\r\n            console.log('Tenant login response:', response);\r\n            this.tenantToken = response.accessToken;\r\n            this.showTenantLogin = false;\r\n            const toast = await this.toastController.create({\r\n              message: 'Pharmacie connectée avec succès !',\r\n              duration: 2000,\r\n              color: 'success',\r\n            });\r\n            toast.present();\r\n          }\r\n        }\r\n      );\r\n  }\r\n\r\n  async userLogin() {\r\n\r\n    // Check if email and password are empty\r\n    if (this.username === '' || this.password === '') {\r\n      const toast = this.toastController.create({\r\n        message: 'Veuillez remplir les champs de connexion.',\r\n        duration: 2000,\r\n        color: 'danger',\r\n      }).then(toast => toast.present());\r\n      return;\r\n    }\r\n\r\n    const loading = await this.loadingController.create({\r\n      message: 'Connexion ...',\r\n    });\r\n    await loading.present();\r\n\r\n    const userRequest: LoginRequest = {\r\n      username: this.username,\r\n      password: this.password,\r\n      tenant_token: this.tenantToken  // Include tenant token in the request\r\n    };\r\n\r\n    this.apiService.userLogin(userRequest, this.tenantToken).subscribe(\r\n      async (response: LoginResponse) => {\r\n        console.log('User login response:', response);\r\n        \r\n        // Store the user data in local storage\r\n        localStorage.setItem('tokenUser', JSON.stringify(response.user_data ?? \"\"));\r\n        localStorage.setItem('tokenTenant', JSON.stringify(response.tenant_data ?? \"\"));\r\n        localStorage.setItem('token', response.local_token ?? \"\");\r\n        localStorage.setItem('ocrMode', 'STANDARD');\r\n\r\n        // Store the credentials in local storage\r\n        this.localCredentials = {\r\n          tenantUsername: this.tenantUsername,\r\n          tenantPassword: this.tenantPassword,\r\n          username: this.username,\r\n          password: this.password,\r\n        };\r\n        localStorage.setItem('credentials', JSON.stringify(this.localCredentials));\r\n        \r\n        const toast = await this.toastController.create({\r\n          message: 'Login successful!',\r\n          duration: 2000,\r\n          color: 'success',\r\n        });\r\n        toast.present();\r\n        await loading.dismiss();\r\n\r\n        if (!this.hasSeenOnboarding) {\r\n          await this.storageService.set('hasSeenOnboarding', true);\r\n          this.navCtrl.navigateRoot('/guide');\r\n        }\r\n        else{\r\n          this.navCtrl.navigateRoot('/scan-bl');\r\n        }\r\n      },\r\n      async (error) => {\r\n        console.error('Login error:', error);\r\n      \r\n        // Extract the error message without \"Internal server error: 400:\"\r\n        let errorMessage = error.error.detail || 'Connexion échouée. Veuillez vérifier vos identifiants.';\r\n        \r\n        // Remove \"Internal server error: 400:\" pattern from the message\r\n        errorMessage = errorMessage.replace(/^Internal server error: \\d+: /, '');\r\n\r\n        const toast = await this.toastController.create({\r\n          message: errorMessage,\r\n          duration: 2000,\r\n          color: 'danger',\r\n        });\r\n        toast.present();\r\n        await loading.dismiss();\r\n      }\r\n    );\r\n  }\r\n\r\n  goBackToTenantLogin() {\r\n    this.showTenantLogin = true;\r\n    this.tenantToken = '';\r\n  }\r\n\r\n  goBackToWelcome() {\r\n    this.navCtrl.navigateRoot('/welcome');\r\n  }\r\n\r\n  async pharmalienLogin() {\r\n    // Check if username and password are empty\r\n    if (this.username === '' || this.password === '') {\r\n      const toast = this.toastController.create({\r\n        message: 'Veuillez remplir les champs de connexion.',\r\n        duration: 2000,\r\n        color: 'danger',\r\n      }).then(toast => toast.present());\r\n      return;\r\n    }\r\n\r\n    const loading = await this.loadingController.create({\r\n      message: 'Connexion ...',\r\n    });\r\n    await loading.present();\r\n\r\n    const pharmalienRequest: PharmalienLoginRequest = {\r\n      username: this.username,\r\n      password: this.password\r\n    };\r\n\r\n    this.apiService.pharmalienLogin(pharmalienRequest).subscribe(\r\n      async (response: PharmalienLoginResponse) => {\r\n        console.log('Pharmalien login response:', response);\r\n\r\n        // Store the user data in local storage for Pharmalien (single token system)\r\n        localStorage.setItem('tokenUser', JSON.stringify(response.user_data ?? \"\"));\r\n        localStorage.setItem('token', response.local_token ?? \"\");\r\n        localStorage.setItem('ocrMode', 'STANDARD');\r\n\r\n        // Store the credentials in local storage\r\n        this.localCredentials = {\r\n          username: this.username,\r\n          password: this.password,\r\n        };\r\n        localStorage.setItem('credentials', JSON.stringify(this.localCredentials));\r\n\r\n        const toast = await this.toastController.create({\r\n          message: 'Login successful!',\r\n          duration: 2000,\r\n          color: 'success',\r\n        });\r\n        toast.present();\r\n        await loading.dismiss();\r\n\r\n        if (!this.hasSeenOnboarding) {\r\n          await this.storageService.set('hasSeenOnboarding', true);\r\n          this.navCtrl.navigateRoot('/guide');\r\n        }\r\n        else{\r\n          this.navCtrl.navigateRoot('/scan-bl');\r\n        }\r\n      },\r\n      async (error) => {\r\n        console.error('Pharmalien login error:', error);\r\n        await loading.dismiss();\r\n      }\r\n    );\r\n  }\r\n}", "<ion-header>\r\n</ion-header>\r\n\r\n<ion-content [fullscreen]=\"true\">\r\n  <div class=\"login-wrapper\">\r\n    <ion-row>\r\n      <ion-col size=\"12\" class=\"login-content\">\r\n        <!-- WinPlus Platform (dual login) -->\r\n        <div *ngIf=\"!isPharmalienPlatform\" class=\"platform-container\">\r\n          <div class=\"step-indicators\">\r\n            <div class=\"step-badge\" [ngClass]=\"{'active': showTenantLogin}\">\r\n              <span class=\"step-number\">1</span>\r\n              <span class=\"step-label\">Pharmacie</span>\r\n            </div>\r\n            <div class=\"step-line\"></div>\r\n            <div class=\"step-badge\" [ngClass]=\"{'active': !showTenantLogin}\">\r\n              <span class=\"step-number\">2</span>\r\n              <span class=\"step-label\">Utilisateur</span>\r\n            </div>\r\n          </div>\r\n          <div class=\"content-login-head\">\r\n            <h1>{{ showTenantLogin ? 'Connexion de la pharmacie' : 'Connexion de l\\'utilisateur' }}</h1>\r\n            <p>Vous devez utiliser <br>les identifiants de WinPlusPharma</p>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Pharmalien Platform (single login) -->\r\n        <div *ngIf=\"isPharmalienPlatform\" class=\"platform-container\">\r\n          <div class=\"content-login-head\">\r\n            <h1>Connexion Pharmalien</h1>\r\n            <p>Vous devez utiliser <br>les identifiants de Pharmalien</p>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- WinPlus Platform Forms -->\r\n        <div *ngIf=\"!isPharmalienPlatform\" class=\"platform-container\">\r\n          <div class=\"inputs-login\" *ngIf=\"showTenantLogin\">\r\n            <ion-item lines=\"none\" class=\"input-item\">\r\n              <ion-input [(ngModel)]=\"tenantUsername\" type=\"text\" placeholder=\"Identifiant de la pharmacie\" required></ion-input>\r\n            </ion-item>\r\n\r\n            <ion-item lines=\"none\" class=\"input-item\">\r\n              <ion-input [(ngModel)]=\"tenantPassword\" type=\"password\" placeholder=\"Mot de passe de la pharmacie\" required></ion-input>\r\n            </ion-item>\r\n            <ion-button expand=\"block\" class=\"login-button\" (click)=\"tenantLogin()\">Se connecter</ion-button>\r\n          </div>\r\n\r\n          <div class=\"inputs-login\" *ngIf=\"!showTenantLogin\">\r\n            <ion-item lines=\"none\" class=\"input-item\">\r\n              <ion-input [(ngModel)]=\"username\" type=\"text\" placeholder=\"Identifiant de l'utilisateur\" required></ion-input>\r\n            </ion-item>\r\n\r\n            <ion-item lines=\"none\" class=\"input-item\">\r\n              <ion-input [(ngModel)]=\"password\" type=\"password\" placeholder=\"Mot de passe de l'utilisateur\" required></ion-input>\r\n            </ion-item>\r\n            <ion-button expand=\"block\" class=\"login-button\" (click)=\"userLogin()\">Se connecter</ion-button>\r\n          </div>\r\n\r\n          <ion-button fill=\"clear\" class=\"back-button\" (click)=\"goBackToTenantLogin()\" *ngIf=\"!showTenantLogin\">\r\n            <ion-icon name=\"arrow-back-outline\" slot=\"start\"></ion-icon>\r\n            Retour à la connexion de la pharmacie\r\n          </ion-button>\r\n        </div>\r\n\r\n        <!-- Pharmalien Platform Form -->\r\n        <div *ngIf=\"isPharmalienPlatform\" class=\"platform-container\">\r\n          <div class=\"inputs-login\">\r\n            <ion-item lines=\"none\" class=\"input-item\">\r\n              <ion-input [(ngModel)]=\"username\" type=\"text\" placeholder=\"Identifiant utilisateur\" required></ion-input>\r\n            </ion-item>\r\n\r\n            <ion-item lines=\"none\" class=\"input-item\">\r\n              <ion-input [(ngModel)]=\"password\" type=\"password\" placeholder=\"Mot de passe\" required></ion-input>\r\n            </ion-item>\r\n            <ion-button expand=\"block\" class=\"login-button\" (click)=\"pharmalienLogin()\">Se connecter</ion-button>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Back to Welcome Button -->\r\n        <ion-button fill=\"clear\" class=\"back-button\" (click)=\"goBackToWelcome()\">\r\n          <ion-icon name=\"arrow-back-outline\" slot=\"start\"></ion-icon>\r\n          Changer de plateforme\r\n        </ion-button>\r\n      </ion-col>\r\n    </ion-row>\r\n\r\n\r\n  </div>\r\n</ion-content>\r\n<ion-footer>\r\n  <img src=\"/assets/sophatel_logo.svg\" alt=\"SOPHATEL Logo\" class=\"logo\">\r\n  <p class=\"copyright text-center\"><span> Sophatel Ingénierie </span> <br>WinDoc © 2025 - Tous droits réservés.</p> \r\n</ion-footer>"], "mappings": ";;AAIA,SAASA,WAAW,QAAQ,gCAAgC;AAC5D,SAASC,QAAQ,QAAQ,qBAAqB;AAC9C,SAASC,KAAK,EAAEC,UAAU,QAAQ,gBAAgB;AAClD,SAASC,EAAE,QAAQ,MAAM;;;;;;;;;;;;ICIXC,EAHN,CAAAC,cAAA,aAA8D,aAC/B,cACqC,eACpC;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAClCH,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAE,MAAA,gBAAS;IACpCF,EADoC,CAAAG,YAAA,EAAO,EACrC;IACNH,EAAA,CAAAI,SAAA,cAA6B;IAE3BJ,EADF,CAAAC,cAAA,cAAiE,eACrC;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAClCH,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAE,MAAA,mBAAW;IAExCF,EAFwC,CAAAG,YAAA,EAAO,EACvC,EACF;IAEJH,EADF,CAAAC,cAAA,eAAgC,UAC1B;IAAAD,EAAA,CAAAE,MAAA,IAAmF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC5FH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAE,MAAA,4BAAoB;IAAAF,EAAA,CAAAI,SAAA,UAAI;IAAAJ,EAAA,CAAAE,MAAA,yCAAiC;IAEhEF,EAFgE,CAAAG,YAAA,EAAI,EAC5D,EACF;;;;IAdsBH,EAAA,CAAAK,SAAA,GAAuC;IAAvCL,EAAA,CAAAM,UAAA,YAAAN,EAAA,CAAAO,eAAA,IAAAC,GAAA,EAAAC,MAAA,CAAAC,eAAA,EAAuC;IAKvCV,EAAA,CAAAK,SAAA,GAAwC;IAAxCL,EAAA,CAAAM,UAAA,YAAAN,EAAA,CAAAO,eAAA,IAAAC,GAAA,GAAAC,MAAA,CAAAC,eAAA,EAAwC;IAM5DV,EAAA,CAAAK,SAAA,GAAmF;IAAnFL,EAAA,CAAAW,iBAAA,CAAAF,MAAA,CAAAC,eAAA,8DAAmF;;;;;IAQvFV,EAFJ,CAAAC,cAAA,aAA6D,cAC3B,SAC1B;IAAAD,EAAA,CAAAE,MAAA,2BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC7BH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,2BAAoB;IAAAF,EAAA,CAAAI,SAAA,SAAI;IAAAJ,EAAA,CAAAE,MAAA,qCAA8B;IAE7DF,EAF6D,CAAAG,YAAA,EAAI,EACzD,EACF;;;;;;IAMAH,EAFJ,CAAAC,cAAA,cAAkD,mBACN,oBAC+D;IAA5FD,EAAA,CAAAY,gBAAA,2BAAAC,kEAAAC,MAAA;MAAAd,EAAA,CAAAe,aAAA,CAAAC,GAAA;MAAA,MAAAP,MAAA,GAAAT,EAAA,CAAAiB,aAAA;MAAAjB,EAAA,CAAAkB,kBAAA,CAAAT,MAAA,CAAAU,cAAA,EAAAL,MAAA,MAAAL,MAAA,CAAAU,cAAA,GAAAL,MAAA;MAAA,OAAAd,EAAA,CAAAoB,WAAA,CAAAN,MAAA;IAAA,EAA4B;IACzCd,EADyG,CAAAG,YAAA,EAAY,EAC1G;IAGTH,EADF,CAAAC,cAAA,mBAA0C,oBACoE;IAAjGD,EAAA,CAAAY,gBAAA,2BAAAS,kEAAAP,MAAA;MAAAd,EAAA,CAAAe,aAAA,CAAAC,GAAA;MAAA,MAAAP,MAAA,GAAAT,EAAA,CAAAiB,aAAA;MAAAjB,EAAA,CAAAkB,kBAAA,CAAAT,MAAA,CAAAa,cAAA,EAAAR,MAAA,MAAAL,MAAA,CAAAa,cAAA,GAAAR,MAAA;MAAA,OAAAd,EAAA,CAAAoB,WAAA,CAAAN,MAAA;IAAA,EAA4B;IACzCd,EAD8G,CAAAG,YAAA,EAAY,EAC/G;IACXH,EAAA,CAAAC,cAAA,qBAAwE;IAAxBD,EAAA,CAAAuB,UAAA,mBAAAC,2DAAA;MAAAxB,EAAA,CAAAe,aAAA,CAAAC,GAAA;MAAA,MAAAP,MAAA,GAAAT,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAoB,WAAA,CAASX,MAAA,CAAAgB,WAAA,EAAa;IAAA,EAAC;IAACzB,EAAA,CAAAE,MAAA,mBAAY;IACtFF,EADsF,CAAAG,YAAA,EAAa,EAC7F;;;;IAPSH,EAAA,CAAAK,SAAA,GAA4B;IAA5BL,EAAA,CAAA0B,gBAAA,YAAAjB,MAAA,CAAAU,cAAA,CAA4B;IAI5BnB,EAAA,CAAAK,SAAA,GAA4B;IAA5BL,EAAA,CAAA0B,gBAAA,YAAAjB,MAAA,CAAAa,cAAA,CAA4B;;;;;;IAOvCtB,EAFJ,CAAAC,cAAA,cAAmD,mBACP,oBAC0D;IAAvFD,EAAA,CAAAY,gBAAA,2BAAAe,kEAAAb,MAAA;MAAAd,EAAA,CAAAe,aAAA,CAAAa,GAAA;MAAA,MAAAnB,MAAA,GAAAT,EAAA,CAAAiB,aAAA;MAAAjB,EAAA,CAAAkB,kBAAA,CAAAT,MAAA,CAAAoB,QAAA,EAAAf,MAAA,MAAAL,MAAA,CAAAoB,QAAA,GAAAf,MAAA;MAAA,OAAAd,EAAA,CAAAoB,WAAA,CAAAN,MAAA;IAAA,EAAsB;IACnCd,EADoG,CAAAG,YAAA,EAAY,EACrG;IAGTH,EADF,CAAAC,cAAA,mBAA0C,oBAC+D;IAA5FD,EAAA,CAAAY,gBAAA,2BAAAkB,kEAAAhB,MAAA;MAAAd,EAAA,CAAAe,aAAA,CAAAa,GAAA;MAAA,MAAAnB,MAAA,GAAAT,EAAA,CAAAiB,aAAA;MAAAjB,EAAA,CAAAkB,kBAAA,CAAAT,MAAA,CAAAsB,QAAA,EAAAjB,MAAA,MAAAL,MAAA,CAAAsB,QAAA,GAAAjB,MAAA;MAAA,OAAAd,EAAA,CAAAoB,WAAA,CAAAN,MAAA;IAAA,EAAsB;IACnCd,EADyG,CAAAG,YAAA,EAAY,EAC1G;IACXH,EAAA,CAAAC,cAAA,qBAAsE;IAAtBD,EAAA,CAAAuB,UAAA,mBAAAS,2DAAA;MAAAhC,EAAA,CAAAe,aAAA,CAAAa,GAAA;MAAA,MAAAnB,MAAA,GAAAT,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAoB,WAAA,CAASX,MAAA,CAAAwB,SAAA,EAAW;IAAA,EAAC;IAACjC,EAAA,CAAAE,MAAA,mBAAY;IACpFF,EADoF,CAAAG,YAAA,EAAa,EAC3F;;;;IAPSH,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAA0B,gBAAA,YAAAjB,MAAA,CAAAoB,QAAA,CAAsB;IAItB7B,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAA0B,gBAAA,YAAAjB,MAAA,CAAAsB,QAAA,CAAsB;;;;;;IAKrC/B,EAAA,CAAAC,cAAA,oBAAsG;IAAzDD,EAAA,CAAAuB,UAAA,mBAAAW,kEAAA;MAAAlC,EAAA,CAAAe,aAAA,CAAAoB,GAAA;MAAA,MAAA1B,MAAA,GAAAT,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAoB,WAAA,CAASX,MAAA,CAAA2B,mBAAA,EAAqB;IAAA,EAAC;IAC1EpC,EAAA,CAAAI,SAAA,kBAA4D;IAC5DJ,EAAA,CAAAE,MAAA,mDACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;;IA1BfH,EAAA,CAAAC,cAAA,aAA8D;IAuB5DD,EAtBA,CAAAqC,UAAA,IAAAC,8BAAA,kBAAkD,IAAAC,8BAAA,kBAWC,IAAAC,qCAAA,yBAWmD;IAIxGxC,EAAA,CAAAG,YAAA,EAAM;;;;IA1BuBH,EAAA,CAAAK,SAAA,EAAqB;IAArBL,EAAA,CAAAM,UAAA,SAAAG,MAAA,CAAAC,eAAA,CAAqB;IAWrBV,EAAA,CAAAK,SAAA,EAAsB;IAAtBL,EAAA,CAAAM,UAAA,UAAAG,MAAA,CAAAC,eAAA,CAAsB;IAW6BV,EAAA,CAAAK,SAAA,EAAsB;IAAtBL,EAAA,CAAAM,UAAA,UAAAG,MAAA,CAAAC,eAAA,CAAsB;;;;;;IAUhGV,EAHN,CAAAC,cAAA,aAA6D,cACjC,mBACkB,oBACqD;IAAlFD,EAAA,CAAAY,gBAAA,2BAAA6B,4DAAA3B,MAAA;MAAAd,EAAA,CAAAe,aAAA,CAAA2B,GAAA;MAAA,MAAAjC,MAAA,GAAAT,EAAA,CAAAiB,aAAA;MAAAjB,EAAA,CAAAkB,kBAAA,CAAAT,MAAA,CAAAoB,QAAA,EAAAf,MAAA,MAAAL,MAAA,CAAAoB,QAAA,GAAAf,MAAA;MAAA,OAAAd,EAAA,CAAAoB,WAAA,CAAAN,MAAA;IAAA,EAAsB;IACnCd,EAD+F,CAAAG,YAAA,EAAY,EAChG;IAGTH,EADF,CAAAC,cAAA,mBAA0C,oBAC8C;IAA3ED,EAAA,CAAAY,gBAAA,2BAAA+B,4DAAA7B,MAAA;MAAAd,EAAA,CAAAe,aAAA,CAAA2B,GAAA;MAAA,MAAAjC,MAAA,GAAAT,EAAA,CAAAiB,aAAA;MAAAjB,EAAA,CAAAkB,kBAAA,CAAAT,MAAA,CAAAsB,QAAA,EAAAjB,MAAA,MAAAL,MAAA,CAAAsB,QAAA,GAAAjB,MAAA;MAAA,OAAAd,EAAA,CAAAoB,WAAA,CAAAN,MAAA;IAAA,EAAsB;IACnCd,EADwF,CAAAG,YAAA,EAAY,EACzF;IACXH,EAAA,CAAAC,cAAA,qBAA4E;IAA5BD,EAAA,CAAAuB,UAAA,mBAAAqB,qDAAA;MAAA5C,EAAA,CAAAe,aAAA,CAAA2B,GAAA;MAAA,MAAAjC,MAAA,GAAAT,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAoB,WAAA,CAASX,MAAA,CAAAoC,eAAA,EAAiB;IAAA,EAAC;IAAC7C,EAAA,CAAAE,MAAA,mBAAY;IAE5FF,EAF4F,CAAAG,YAAA,EAAa,EACjG,EACF;;;;IARWH,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAA0B,gBAAA,YAAAjB,MAAA,CAAAoB,QAAA,CAAsB;IAItB7B,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAA0B,gBAAA,YAAAjB,MAAA,CAAAsB,QAAA,CAAsB;;;ADzD/C,OAAM,MAAOe,SAAS;EAYpBC,YACUC,OAAsB,EACtBC,UAAsB,EACtBC,eAAgC,EAChCC,iBAAoC,EACpCC,cAA8B;IAJ9B,KAAAJ,OAAO,GAAPA,OAAO;IACP,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,cAAc,GAAdA,cAAc;IAhBxB,KAAAjC,cAAc,GAAW,EAAE;IAC3B,KAAAG,cAAc,GAAW,EAAE;IAC3B,KAAAO,QAAQ,GAAW,EAAE;IACrB,KAAAE,QAAQ,GAAW,EAAE;IACrB,KAAArB,eAAe,GAAY,IAAI;IAC/B,KAAA2C,WAAW,GAAW,EAAE;IACxB,KAAAC,iBAAiB,GAAY,KAAK;IAClC,KAAAC,gBAAgB,GAAQ,EAAE;IAC1B,KAAAC,gBAAgB,GAAmC,eAAe;IAClE,KAAAC,oBAAoB,GAAG,KAAK;EAQzB;EAEGC,QAAQA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACZ,IAAIjE,WAAW,CAACkE,UAAU,EAAE;QAC1BF,KAAI,CAACxC,cAAc,GAAG,EAAE;QACxBwC,KAAI,CAACrC,cAAc,GAAG,EAAE;QACxBqC,KAAI,CAAC9B,QAAQ,GAAG,EAAE;QAClB8B,KAAI,CAAC5B,QAAQ,GAAG,EAAE;OACnB,MAAM;QACL4B,KAAI,CAACxC,cAAc,GAAG,MAAM;QAC5BwC,KAAI,CAACrC,cAAc,GAAG,QAAQ;QAC9BqC,KAAI,CAAC9B,QAAQ,GAAG,IAAI;QACpB8B,KAAI,CAAC5B,QAAQ,GAAG,IAAI;;MAItB;MACA4B,KAAI,CAACJ,gBAAgB,SAASO,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAQ,CAAC;MACpF,IAAIN,KAAI,CAACJ,gBAAgB,EAAE;QACzBI,KAAI,CAACxC,cAAc,GAAGwC,KAAI,CAACJ,gBAAgB,CAACpC,cAAc;QAC1DwC,KAAI,CAACrC,cAAc,GAAGqC,KAAI,CAACJ,gBAAgB,CAACjC,cAAc;QAC1DqC,KAAI,CAAC9B,QAAQ,GAAG8B,KAAI,CAACJ,gBAAgB,CAAC1B,QAAQ;QAC9C8B,KAAI,CAAC5B,QAAQ,GAAG4B,KAAI,CAACJ,gBAAgB,CAACxB,QAAQ;;MAGhDnC,QAAQ,CAACsE,WAAW,CAAC,kBAAkB,EAAE,MAAK;QAC5C,MAAMC,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,YAAY,CAAC;QACnDF,MAAM,aAANA,MAAM,eAANA,MAAM,CAAEG,SAAS,CAACC,GAAG,CAAC,eAAe,CAAC;MACxC,CAAC,CAAC;MAEF3E,QAAQ,CAACsE,WAAW,CAAC,kBAAkB,EAAE,MAAK;QAC5C,MAAMC,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,YAAY,CAAC;QACnDF,MAAM,aAANA,MAAM,eAANA,MAAM,CAAEG,SAAS,CAACE,MAAM,CAAC,eAAe,CAAC;MAC3C,CAAC,CAAC;MAEF;MACA,MAAMb,KAAI,CAACP,cAAc,CAACqB,IAAI,EAAE;MAEhC;MACAd,KAAI,CAACL,iBAAiB,SAASK,KAAI,CAACP,cAAc,CAACsB,GAAG,CAAC,mBAAmB,CAAC;MAE3E;MACA,MAAMC,QAAQ,GAAGX,YAAY,CAACC,OAAO,CAAC,SAAS,CAAmC;MAClF,IAAIU,QAAQ,EAAE;QACZhB,KAAI,CAACH,gBAAgB,GAAGmB,QAAQ;QAChChB,KAAI,CAACF,oBAAoB,GAAGkB,QAAQ,KAAK,YAAY;QACrDC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEF,QAAQ,CAAC;OAC5C,MAAM;QACL;QACAhB,KAAI,CAACX,OAAO,CAAC8B,YAAY,CAAC,UAAU,CAAC;;IACtC;EACH;EAGAC,WAAWA,CAAA;IACTnF,QAAQ,CAACoF,kBAAkB,EAAE;EAC/B;EAEMvD,WAAWA,CAAA;IAAA,IAAAwD,MAAA;IAAA,OAAArB,iBAAA;MAEf;MACA,IAAIqB,MAAI,CAAC9D,cAAc,KAAK,EAAE,IAAI8D,MAAI,CAAC3D,cAAc,KAAK,EAAE,EAAE;QAC5D,MAAM4D,KAAK,GAAGD,MAAI,CAAC/B,eAAe,CAACiC,MAAM,CAAC;UACxCC,OAAO,EAAE,2CAA2C;UACpDC,QAAQ,EAAE,IAAI;UACdC,KAAK,EAAE;SACR,CAAC,CAACC,IAAI,CAACL,KAAK,IAAIA,KAAK,CAACM,OAAO,EAAE,CAAC;QACjC;;MAGF,MAAMC,OAAO,SAASR,MAAI,CAAC9B,iBAAiB,CAACgC,MAAM,CAAC;QAClDC,OAAO,EAAE;OACV,CAAC;MACF,MAAMK,OAAO,CAACD,OAAO,EAAE;MAEvB,MAAME,aAAa,GAAuB;QACxC7D,QAAQ,EAAEoD,MAAI,CAAC9D,cAAc;QAC7BY,QAAQ,EAAEkD,MAAI,CAAC3D;OAChB;MAED2D,MAAI,CAAChC,UAAU,CAACxB,WAAW,CAACiE,aAAa,CAAC,CACvCC,IAAI,CACH9F,KAAK,CAAC,CAAC,CAAC;MAAE;MACVC,UAAU,CAAC8F,KAAK,IAAG;QACjBhB,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEe,KAAK,CAAC;QAEzC,IAAGA,KAAK,CAACC,MAAM,KAAK,GAAG,EAAC;UACpB,MAAMX,KAAK,GAAID,MAAI,CAAC/B,eAAe,CAACiC,MAAM,CAAC;YACzCC,OAAO,EAAEQ,KAAK,CAACA,KAAK,CAACE,MAAM;YAC3BT,QAAQ,EAAE,IAAI;YACdC,KAAK,EAAE;WACR,CAAC,CAACC,IAAI,CAACL,KAAK,IAAIA,KAAK,CAACM,OAAO,EAAE,CAAC;SACpC,MACG;UACF,MAAMN,KAAK,GAAGD,MAAI,CAAC/B,eAAe,CAACiC,MAAM,CAAC;YACxCC,OAAO,EAAE,4DAA4D;YACrEC,QAAQ,EAAE,IAAI;YACdC,KAAK,EAAE;WACR,CAAC,CAACC,IAAI,CAACL,KAAK,IAAIA,KAAK,CAACM,OAAO,EAAE,CAAC;;QAEnCZ,OAAO,CAACgB,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;QAC3C,OAAO7F,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CACH,CACAgG,SAAS;QAAA,IAAAC,IAAA,GAAApC,iBAAA,CACR,WAAOqC,QAAoC,EAAI;UAC7C,MAAMR,OAAO,CAACS,OAAO,EAAE;UACvB,IAAID,QAAQ,EAAE;YACZrB,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEoB,QAAQ,CAAC;YAC/ChB,MAAI,CAAC5B,WAAW,GAAG4C,QAAQ,CAACE,WAAW;YACvClB,MAAI,CAACvE,eAAe,GAAG,KAAK;YAC5B,MAAMwE,KAAK,SAASD,MAAI,CAAC/B,eAAe,CAACiC,MAAM,CAAC;cAC9CC,OAAO,EAAE,mCAAmC;cAC5CC,QAAQ,EAAE,IAAI;cACdC,KAAK,EAAE;aACR,CAAC;YACFJ,KAAK,CAACM,OAAO,EAAE;;QAEnB,CAAC;QAAA,iBAAAY,EAAA;UAAA,OAAAJ,IAAA,CAAAK,KAAA,OAAAC,SAAA;QAAA;MAAA,IACF;IAAC;EACN;EAEMrE,SAASA,CAAA;IAAA,IAAAsE,MAAA;IAAA,OAAA3C,iBAAA;MAEb;MACA,IAAI2C,MAAI,CAAC1E,QAAQ,KAAK,EAAE,IAAI0E,MAAI,CAACxE,QAAQ,KAAK,EAAE,EAAE;QAChD,MAAMmD,KAAK,GAAGqB,MAAI,CAACrD,eAAe,CAACiC,MAAM,CAAC;UACxCC,OAAO,EAAE,2CAA2C;UACpDC,QAAQ,EAAE,IAAI;UACdC,KAAK,EAAE;SACR,CAAC,CAACC,IAAI,CAACL,KAAK,IAAIA,KAAK,CAACM,OAAO,EAAE,CAAC;QACjC;;MAGF,MAAMC,OAAO,SAASc,MAAI,CAACpD,iBAAiB,CAACgC,MAAM,CAAC;QAClDC,OAAO,EAAE;OACV,CAAC;MACF,MAAMK,OAAO,CAACD,OAAO,EAAE;MAEvB,MAAMgB,WAAW,GAAiB;QAChC3E,QAAQ,EAAE0E,MAAI,CAAC1E,QAAQ;QACvBE,QAAQ,EAAEwE,MAAI,CAACxE,QAAQ;QACvB0E,YAAY,EAAEF,MAAI,CAAClD,WAAW,CAAE;OACjC;MAEDkD,MAAI,CAACtD,UAAU,CAAChB,SAAS,CAACuE,WAAW,EAAED,MAAI,CAAClD,WAAW,CAAC,CAAC0C,SAAS;QAAA,IAAAW,KAAA,GAAA9C,iBAAA,CAChE,WAAOqC,QAAuB,EAAI;UAAA,IAAAU,mBAAA,EAAAC,qBAAA,EAAAC,qBAAA;UAChCjC,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEoB,QAAQ,CAAC;UAE7C;UACAjC,YAAY,CAAC8C,OAAO,CAAC,WAAW,EAAEhD,IAAI,CAACiD,SAAS,EAAAJ,mBAAA,GAACV,QAAQ,CAACe,SAAS,cAAAL,mBAAA,cAAAA,mBAAA,GAAI,EAAE,CAAC,CAAC;UAC3E3C,YAAY,CAAC8C,OAAO,CAAC,aAAa,EAAEhD,IAAI,CAACiD,SAAS,EAAAH,qBAAA,GAACX,QAAQ,CAACgB,WAAW,cAAAL,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC,CAAC;UAC/E5C,YAAY,CAAC8C,OAAO,CAAC,OAAO,GAAAD,qBAAA,GAAEZ,QAAQ,CAACiB,WAAW,cAAAL,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;UACzD7C,YAAY,CAAC8C,OAAO,CAAC,SAAS,EAAE,UAAU,CAAC;UAE3C;UACAP,MAAI,CAAChD,gBAAgB,GAAG;YACtBpC,cAAc,EAAEoF,MAAI,CAACpF,cAAc;YACnCG,cAAc,EAAEiF,MAAI,CAACjF,cAAc;YACnCO,QAAQ,EAAE0E,MAAI,CAAC1E,QAAQ;YACvBE,QAAQ,EAAEwE,MAAI,CAACxE;WAChB;UACDiC,YAAY,CAAC8C,OAAO,CAAC,aAAa,EAAEhD,IAAI,CAACiD,SAAS,CAACR,MAAI,CAAChD,gBAAgB,CAAC,CAAC;UAE1E,MAAM2B,KAAK,SAASqB,MAAI,CAACrD,eAAe,CAACiC,MAAM,CAAC;YAC9CC,OAAO,EAAE,mBAAmB;YAC5BC,QAAQ,EAAE,IAAI;YACdC,KAAK,EAAE;WACR,CAAC;UACFJ,KAAK,CAACM,OAAO,EAAE;UACf,MAAMC,OAAO,CAACS,OAAO,EAAE;UAEvB,IAAI,CAACK,MAAI,CAACjD,iBAAiB,EAAE;YAC3B,MAAMiD,MAAI,CAACnD,cAAc,CAAC+D,GAAG,CAAC,mBAAmB,EAAE,IAAI,CAAC;YACxDZ,MAAI,CAACvD,OAAO,CAAC8B,YAAY,CAAC,QAAQ,CAAC;WACpC,MACG;YACFyB,MAAI,CAACvD,OAAO,CAAC8B,YAAY,CAAC,UAAU,CAAC;;QAEzC,CAAC;QAAA,iBAAAsC,GAAA;UAAA,OAAAV,KAAA,CAAAL,KAAA,OAAAC,SAAA;QAAA;MAAA;QAAA,IAAAe,KAAA,GAAAzD,iBAAA,CACD,WAAOgC,KAAK,EAAI;UACdhB,OAAO,CAACgB,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;UAEpC;UACA,IAAI0B,YAAY,GAAG1B,KAAK,CAACA,KAAK,CAACE,MAAM,IAAI,wDAAwD;UAEjG;UACAwB,YAAY,GAAGA,YAAY,CAACC,OAAO,CAAC,+BAA+B,EAAE,EAAE,CAAC;UAExE,MAAMrC,KAAK,SAASqB,MAAI,CAACrD,eAAe,CAACiC,MAAM,CAAC;YAC9CC,OAAO,EAAEkC,YAAY;YACrBjC,QAAQ,EAAE,IAAI;YACdC,KAAK,EAAE;WACR,CAAC;UACFJ,KAAK,CAACM,OAAO,EAAE;UACf,MAAMC,OAAO,CAACS,OAAO,EAAE;QACzB,CAAC;QAAA,iBAAAsB,GAAA;UAAA,OAAAH,KAAA,CAAAhB,KAAA,OAAAC,SAAA;QAAA;MAAA,IACF;IAAC;EACJ;EAEAlE,mBAAmBA,CAAA;IACjB,IAAI,CAAC1B,eAAe,GAAG,IAAI;IAC3B,IAAI,CAAC2C,WAAW,GAAG,EAAE;EACvB;EAEAoE,eAAeA,CAAA;IACb,IAAI,CAACzE,OAAO,CAAC8B,YAAY,CAAC,UAAU,CAAC;EACvC;EAEMjC,eAAeA,CAAA;IAAA,IAAA6E,MAAA;IAAA,OAAA9D,iBAAA;MACnB;MACA,IAAI8D,MAAI,CAAC7F,QAAQ,KAAK,EAAE,IAAI6F,MAAI,CAAC3F,QAAQ,KAAK,EAAE,EAAE;QAChD,MAAMmD,KAAK,GAAGwC,MAAI,CAACxE,eAAe,CAACiC,MAAM,CAAC;UACxCC,OAAO,EAAE,2CAA2C;UACpDC,QAAQ,EAAE,IAAI;UACdC,KAAK,EAAE;SACR,CAAC,CAACC,IAAI,CAACL,KAAK,IAAIA,KAAK,CAACM,OAAO,EAAE,CAAC;QACjC;;MAGF,MAAMC,OAAO,SAASiC,MAAI,CAACvE,iBAAiB,CAACgC,MAAM,CAAC;QAClDC,OAAO,EAAE;OACV,CAAC;MACF,MAAMK,OAAO,CAACD,OAAO,EAAE;MAEvB,MAAMmC,iBAAiB,GAA2B;QAChD9F,QAAQ,EAAE6F,MAAI,CAAC7F,QAAQ;QACvBE,QAAQ,EAAE2F,MAAI,CAAC3F;OAChB;MAED2F,MAAI,CAACzE,UAAU,CAACJ,eAAe,CAAC8E,iBAAiB,CAAC,CAAC5B,SAAS;QAAA,IAAA6B,KAAA,GAAAhE,iBAAA,CAC1D,WAAOqC,QAAiC,EAAI;UAAA,IAAA4B,oBAAA,EAAAC,sBAAA;UAC1ClD,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEoB,QAAQ,CAAC;UAEnD;UACAjC,YAAY,CAAC8C,OAAO,CAAC,WAAW,EAAEhD,IAAI,CAACiD,SAAS,EAAAc,oBAAA,GAAC5B,QAAQ,CAACe,SAAS,cAAAa,oBAAA,cAAAA,oBAAA,GAAI,EAAE,CAAC,CAAC;UAC3E7D,YAAY,CAAC8C,OAAO,CAAC,OAAO,GAAAgB,sBAAA,GAAE7B,QAAQ,CAACiB,WAAW,cAAAY,sBAAA,cAAAA,sBAAA,GAAI,EAAE,CAAC;UACzD9D,YAAY,CAAC8C,OAAO,CAAC,SAAS,EAAE,UAAU,CAAC;UAE3C;UACAY,MAAI,CAACnE,gBAAgB,GAAG;YACtB1B,QAAQ,EAAE6F,MAAI,CAAC7F,QAAQ;YACvBE,QAAQ,EAAE2F,MAAI,CAAC3F;WAChB;UACDiC,YAAY,CAAC8C,OAAO,CAAC,aAAa,EAAEhD,IAAI,CAACiD,SAAS,CAACW,MAAI,CAACnE,gBAAgB,CAAC,CAAC;UAE1E,MAAM2B,KAAK,SAASwC,MAAI,CAACxE,eAAe,CAACiC,MAAM,CAAC;YAC9CC,OAAO,EAAE,mBAAmB;YAC5BC,QAAQ,EAAE,IAAI;YACdC,KAAK,EAAE;WACR,CAAC;UACFJ,KAAK,CAACM,OAAO,EAAE;UACf,MAAMC,OAAO,CAACS,OAAO,EAAE;UAEvB,IAAI,CAACwB,MAAI,CAACpE,iBAAiB,EAAE;YAC3B,MAAMoE,MAAI,CAACtE,cAAc,CAAC+D,GAAG,CAAC,mBAAmB,EAAE,IAAI,CAAC;YACxDO,MAAI,CAAC1E,OAAO,CAAC8B,YAAY,CAAC,QAAQ,CAAC;WACpC,MACG;YACF4C,MAAI,CAAC1E,OAAO,CAAC8B,YAAY,CAAC,UAAU,CAAC;;QAEzC,CAAC;QAAA,iBAAAiD,GAAA;UAAA,OAAAH,KAAA,CAAAvB,KAAA,OAAAC,SAAA;QAAA;MAAA;QAAA,IAAA0B,KAAA,GAAApE,iBAAA,CACD,WAAOgC,KAAK,EAAI;UACdhB,OAAO,CAACgB,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;UAC/C,MAAMH,OAAO,CAACS,OAAO,EAAE;QACzB,CAAC;QAAA,iBAAA+B,GAAA;UAAA,OAAAD,KAAA,CAAA3B,KAAA,OAAAC,SAAA;QAAA;MAAA,IACF;IAAC;EACJ;;aA7RWxD,SAAS;;mBAATA,UAAS,EAAA9C,EAAA,CAAAkI,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAApI,EAAA,CAAAkI,iBAAA,CAAAG,EAAA,CAAAC,UAAA,GAAAtI,EAAA,CAAAkI,iBAAA,CAAAC,EAAA,CAAAI,eAAA,GAAAvI,EAAA,CAAAkI,iBAAA,CAAAC,EAAA,CAAAK,iBAAA,GAAAxI,EAAA,CAAAkI,iBAAA,CAAAO,EAAA,CAAAC,cAAA;AAAA;;QAAT5F,UAAS;EAAA6F,SAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,mBAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MCftBjJ,EAAA,CAAAI,SAAA,iBACa;MAKPJ,EAHN,CAAAC,cAAA,qBAAiC,aACJ,cAChB,iBACkC;MA2DvCD,EAzDA,CAAAqC,UAAA,IAAA8G,wBAAA,kBAA8D,IAAAC,wBAAA,iBAmBD,IAAAC,wBAAA,iBAQC,IAAAC,wBAAA,iBA8BD;MAc7DtJ,EAAA,CAAAC,cAAA,oBAAyE;MAA5BD,EAAA,CAAAuB,UAAA,mBAAAgI,+CAAA;QAAA,OAASL,GAAA,CAAAzB,eAAA,EAAiB;MAAA,EAAC;MACtEzH,EAAA,CAAAI,SAAA,mBAA4D;MAC5DJ,EAAA,CAAAE,MAAA,+BACF;MAMRF,EANQ,CAAAG,YAAA,EAAa,EACL,EACF,EAGN,EACM;MACdH,EAAA,CAAAC,cAAA,kBAAY;MACVD,EAAA,CAAAI,SAAA,cAAsE;MACrCJ,EAAjC,CAAAC,cAAA,YAAiC,YAAM;MAACD,EAAA,CAAAE,MAAA,kCAAoB;MAAAF,EAAA,CAAAG,YAAA,EAAO;MAACH,EAAA,CAAAI,SAAA,UAAI;MAAAJ,EAAA,CAAAE,MAAA,4DAAqC;MAC/GF,EAD+G,CAAAG,YAAA,EAAI,EACtG;;;MAzFAH,EAAA,CAAAK,SAAA,EAAmB;MAAnBL,EAAA,CAAAM,UAAA,oBAAmB;MAKlBN,EAAA,CAAAK,SAAA,GAA2B;MAA3BL,EAAA,CAAAM,UAAA,UAAA4I,GAAA,CAAAzF,oBAAA,CAA2B;MAmB3BzD,EAAA,CAAAK,SAAA,EAA0B;MAA1BL,EAAA,CAAAM,UAAA,SAAA4I,GAAA,CAAAzF,oBAAA,CAA0B;MAQ1BzD,EAAA,CAAAK,SAAA,EAA2B;MAA3BL,EAAA,CAAAM,UAAA,UAAA4I,GAAA,CAAAzF,oBAAA,CAA2B;MA8B3BzD,EAAA,CAAAK,SAAA,EAA0B;MAA1BL,EAAA,CAAAM,UAAA,SAAA4I,GAAA,CAAAzF,oBAAA,CAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}