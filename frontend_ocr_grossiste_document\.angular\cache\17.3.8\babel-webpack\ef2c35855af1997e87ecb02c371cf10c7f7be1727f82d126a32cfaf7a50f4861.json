{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Downloads/Work __Abder<PERSON>mane_ouhna/OCR_DOCUMENT_GROSSISTE/Frontend ocr grossiste document/frontend_ocr_grossiste_document/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nvar _LoginPage;\nimport { environment } from '../../environments/environment';\nimport { Keyboard } from '@capacitor/keyboard';\nimport { retry, catchError } from 'rxjs/operators';\nimport { of } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ionic/angular\";\nimport * as i2 from \"../services/api.service\";\nimport * as i3 from \"../services/storage.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nconst _c0 = a0 => ({\n  \"active\": a0\n});\nfunction LoginPage_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 8)(2, \"div\", 9)(3, \"span\", 10);\n    i0.ɵɵtext(4, \"1\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 11);\n    i0.ɵɵtext(6, \"Pharmacie\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(7, \"div\", 12);\n    i0.ɵɵelementStart(8, \"div\", 9)(9, \"span\", 10);\n    i0.ɵɵtext(10, \"2\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\", 11);\n    i0.ɵɵtext(12, \"Utilisateur\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(13, \"div\", 13)(14, \"h1\");\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"p\");\n    i0.ɵɵtext(17, \"Vous devez utiliser \");\n    i0.ɵɵelement(18, \"br\");\n    i0.ɵɵtext(19, \"les identifiants de WinPlusPharma\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(3, _c0, ctx_r0.showTenantLogin));\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(5, _c0, !ctx_r0.showTenantLogin));\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r0.showTenantLogin ? \"Connexion de la pharmacie\" : \"Connexion de l'utilisateur\");\n  }\n}\nfunction LoginPage_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 13)(2, \"h1\");\n    i0.ɵɵtext(3, \"Connexion Pharmalien\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\");\n    i0.ɵɵtext(5, \"Vous devez utiliser \");\n    i0.ɵɵelement(6, \"br\");\n    i0.ɵɵtext(7, \"les identifiants de Pharmalien\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction LoginPage_div_7_div_1_ion_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ion-button\", 21);\n    i0.ɵɵlistener(\"click\", function LoginPage_div_7_div_1_ion_button_5_Template_ion_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r0.tenantLogin());\n    });\n    i0.ɵɵtext(1, \"Se connecter\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginPage_div_7_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 16)(1, \"ion-item\", 17)(2, \"ion-input\", 18);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function LoginPage_div_7_div_1_Template_ion_input_ngModelChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r0.tenantUsername, $event) || (ctx_r0.tenantUsername = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"ion-item\", 17)(4, \"ion-input\", 19);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function LoginPage_div_7_div_1_Template_ion_input_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r0.tenantPassword, $event) || (ctx_r0.tenantPassword = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(5, LoginPage_div_7_div_1_ion_button_5_Template, 2, 0, \"ion-button\", 20);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r0.tenantUsername);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r0.tenantPassword);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.showTenantLogin);\n  }\n}\nfunction LoginPage_div_7_div_2_ion_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ion-button\", 21);\n    i0.ɵɵlistener(\"click\", function LoginPage_div_7_div_2_ion_button_5_Template_ion_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r0 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r0.userLogin());\n    });\n    i0.ɵɵtext(1, \"Se connecter\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginPage_div_7_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 16)(1, \"ion-item\", 17)(2, \"ion-input\", 22);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function LoginPage_div_7_div_2_Template_ion_input_ngModelChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r0.username, $event) || (ctx_r0.username = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"ion-item\", 17)(4, \"ion-input\", 23);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function LoginPage_div_7_div_2_Template_ion_input_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r0.password, $event) || (ctx_r0.password = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(5, LoginPage_div_7_div_2_ion_button_5_Template, 2, 0, \"ion-button\", 20);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r0.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r0.password);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.showTenantLogin);\n  }\n}\nfunction LoginPage_div_7_ion_button_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ion-button\", 4);\n    i0.ɵɵlistener(\"click\", function LoginPage_div_7_ion_button_3_Template_ion_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.goBackToTenantLogin());\n    });\n    i0.ɵɵelement(1, \"ion-icon\", 5);\n    i0.ɵɵtext(2, \" Retour \\u00E0 la connexion de la pharmacie \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginPage_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, LoginPage_div_7_div_1_Template, 6, 3, \"div\", 14)(2, LoginPage_div_7_div_2_Template, 6, 3, \"div\", 14)(3, LoginPage_div_7_ion_button_3_Template, 3, 0, \"ion-button\", 15);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.showTenantLogin);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.showTenantLogin);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.showTenantLogin);\n  }\n}\nfunction LoginPage_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 16)(2, \"ion-item\", 17)(3, \"ion-input\", 24);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function LoginPage_div_8_Template_ion_input_ngModelChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r0 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r0.username, $event) || (ctx_r0.username = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"ion-item\", 17)(5, \"ion-input\", 25);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function LoginPage_div_8_Template_ion_input_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r0 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r0.password, $event) || (ctx_r0.password = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"ion-button\", 21);\n    i0.ɵɵlistener(\"click\", function LoginPage_div_8_Template_ion_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.pharmalienLogin());\n    });\n    i0.ɵɵtext(7, \"Se connecter\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r0.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r0.password);\n  }\n}\nexport class LoginPage {\n  constructor(navCtrl, apiService, toastController, loadingController, storageService) {\n    this.navCtrl = navCtrl;\n    this.apiService = apiService;\n    this.toastController = toastController;\n    this.loadingController = loadingController;\n    this.storageService = storageService;\n    this.tenantUsername = '';\n    this.tenantPassword = '';\n    this.username = '';\n    this.password = '';\n    this.showTenantLogin = true;\n    this.tenantToken = '';\n    this.hasSeenOnboarding = false;\n    this.localCredentials = {};\n    this.selectedPlatform = 'winpluspharma';\n    this.isPharmalienPlatform = false;\n  }\n  ngOnInit() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (environment.production) {\n        _this.tenantUsername = '';\n        _this.tenantPassword = '';\n        _this.username = '';\n        _this.password = '';\n      } else {\n        _this.tenantUsername = '0001';\n        _this.tenantPassword = '123456';\n        _this.username = 'PH';\n        _this.password = 'PH';\n      }\n      // Load the credentials from the local storage\n      _this.localCredentials = yield JSON.parse(localStorage.getItem('credentials'));\n      if (_this.localCredentials) {\n        _this.tenantUsername = _this.localCredentials.tenantUsername;\n        _this.tenantPassword = _this.localCredentials.tenantPassword;\n        _this.username = _this.localCredentials.username;\n        _this.password = _this.localCredentials.password;\n      }\n      Keyboard.addListener('keyboardWillShow', () => {\n        const footer = document.querySelector('ion-footer');\n        footer === null || footer === void 0 || footer.classList.add('keyboard-open');\n      });\n      Keyboard.addListener('keyboardWillHide', () => {\n        const footer = document.querySelector('ion-footer');\n        footer === null || footer === void 0 || footer.classList.remove('keyboard-open');\n      });\n      // Initialize storage\n      yield _this.storageService.init();\n      // Check if the user has seen onboarding\n      _this.hasSeenOnboarding = yield _this.storageService.get('hasSeenOnboarding');\n      // Detect selected platform\n      const platform = localStorage.getItem('src_app');\n      if (platform) {\n        _this.selectedPlatform = platform;\n        _this.isPharmalienPlatform = platform === 'pharmalier';\n        console.log('Platform detected:', platform);\n      } else {\n        // If no platform selected, redirect back to welcome\n        _this.navCtrl.navigateRoot('/welcome');\n      }\n    })();\n  }\n  ngOnDestroy() {\n    Keyboard.removeAllListeners();\n  }\n  tenantLogin() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      // Check if email and password are empty\n      if (_this2.tenantUsername === '' || _this2.tenantPassword === '') {\n        const toast = _this2.toastController.create({\n          message: 'Veuillez remplir les champs de connexion.',\n          duration: 2000,\n          color: 'danger'\n        }).then(toast => toast.present());\n        return;\n      }\n      const loading = yield _this2.loadingController.create({\n        message: 'Connexion de la pharmacie...'\n      });\n      yield loading.present();\n      const tenantRequest = {\n        username: _this2.tenantUsername,\n        password: _this2.tenantPassword\n      };\n      _this2.apiService.tenantLogin(tenantRequest).pipe(retry(1),\n      // Retry the request once if it fails\n      catchError(error => {\n        console.log('Tenant login error:', error);\n        if (error.status === 403) {\n          const toast = _this2.toastController.create({\n            message: error.error.detail,\n            duration: 2000,\n            color: 'danger'\n          }).then(toast => toast.present());\n        } else {\n          const toast = _this2.toastController.create({\n            message: 'La connexion de la pharmacie a échoué. Veuillez réessayer.',\n            duration: 2000,\n            color: 'danger'\n          }).then(toast => toast.present());\n        }\n        console.error('Tenant login error:', error);\n        return of(null); // Return an observable with null to continue the stream\n      })).subscribe( /*#__PURE__*/function () {\n        var _ref = _asyncToGenerator(function* (response) {\n          yield loading.dismiss();\n          if (response) {\n            console.log('Tenant login response:', response);\n            _this2.tenantToken = response.accessToken;\n            _this2.showTenantLogin = false;\n            const toast = yield _this2.toastController.create({\n              message: 'Pharmacie connectée avec succès !',\n              duration: 2000,\n              color: 'success'\n            });\n            toast.present();\n          }\n        });\n        return function (_x) {\n          return _ref.apply(this, arguments);\n        };\n      }());\n    })();\n  }\n  userLogin() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      // Check if email and password are empty\n      if (_this3.username === '' || _this3.password === '') {\n        const toast = _this3.toastController.create({\n          message: 'Veuillez remplir les champs de connexion.',\n          duration: 2000,\n          color: 'danger'\n        }).then(toast => toast.present());\n        return;\n      }\n      const loading = yield _this3.loadingController.create({\n        message: 'Connexion ...'\n      });\n      yield loading.present();\n      const userRequest = {\n        username: _this3.username,\n        password: _this3.password,\n        tenant_token: _this3.tenantToken // Include tenant token in the request\n      };\n      _this3.apiService.userLogin(userRequest, _this3.tenantToken).subscribe( /*#__PURE__*/function () {\n        var _ref2 = _asyncToGenerator(function* (response) {\n          var _response$user_data, _response$tenant_data, _response$local_token;\n          console.log('User login response:', response);\n          // Store the user data in local storage\n          localStorage.setItem('tokenUser', JSON.stringify((_response$user_data = response.user_data) !== null && _response$user_data !== void 0 ? _response$user_data : \"\"));\n          localStorage.setItem('tokenTenant', JSON.stringify((_response$tenant_data = response.tenant_data) !== null && _response$tenant_data !== void 0 ? _response$tenant_data : \"\"));\n          localStorage.setItem('token', (_response$local_token = response.local_token) !== null && _response$local_token !== void 0 ? _response$local_token : \"\");\n          localStorage.setItem('ocrMode', 'STANDARD');\n          // Store the credentials in local storage\n          _this3.localCredentials = {\n            tenantUsername: _this3.tenantUsername,\n            tenantPassword: _this3.tenantPassword,\n            username: _this3.username,\n            password: _this3.password\n          };\n          localStorage.setItem('credentials', JSON.stringify(_this3.localCredentials));\n          const toast = yield _this3.toastController.create({\n            message: 'Login successful!',\n            duration: 2000,\n            color: 'success'\n          });\n          toast.present();\n          yield loading.dismiss();\n          if (!_this3.hasSeenOnboarding) {\n            yield _this3.storageService.set('hasSeenOnboarding', true);\n            _this3.navCtrl.navigateRoot('/guide');\n          } else {\n            _this3.navCtrl.navigateRoot('/scan-bl');\n          }\n        });\n        return function (_x2) {\n          return _ref2.apply(this, arguments);\n        };\n      }(), /*#__PURE__*/function () {\n        var _ref3 = _asyncToGenerator(function* (error) {\n          console.error('Login error:', error);\n          // Extract the error message without \"Internal server error: 400:\"\n          let errorMessage = error.error.detail || 'Connexion échouée. Veuillez vérifier vos identifiants.';\n          // Remove \"Internal server error: 400:\" pattern from the message\n          errorMessage = errorMessage.replace(/^Internal server error: \\d+: /, '');\n          const toast = yield _this3.toastController.create({\n            message: errorMessage,\n            duration: 2000,\n            color: 'danger'\n          });\n          toast.present();\n          yield loading.dismiss();\n        });\n        return function (_x3) {\n          return _ref3.apply(this, arguments);\n        };\n      }());\n    })();\n  }\n  goBackToTenantLogin() {\n    this.showTenantLogin = true;\n    this.tenantToken = '';\n  }\n  goBackToWelcome() {\n    this.navCtrl.navigateRoot('/welcome');\n  }\n  pharmalienLogin() {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      // Check if username and password are empty\n      if (_this4.username === '' || _this4.password === '') {\n        const toast = _this4.toastController.create({\n          message: 'Veuillez remplir les champs de connexion.',\n          duration: 2000,\n          color: 'danger'\n        }).then(toast => toast.present());\n        return;\n      }\n      const loading = yield _this4.loadingController.create({\n        message: 'Connexion ...'\n      });\n      yield loading.present();\n      const pharmalienRequest = {\n        username: _this4.username,\n        password: _this4.password\n      };\n      _this4.apiService.pharmalienLogin(pharmalienRequest).subscribe( /*#__PURE__*/function () {\n        var _ref4 = _asyncToGenerator(function* (response) {\n          var _response$user_data2, _response$local_token2;\n          console.log('Pharmalien login response:', response);\n          // Store the user data in local storage for Pharmalien (single token system)\n          localStorage.setItem('tokenUser', JSON.stringify((_response$user_data2 = response.user_data) !== null && _response$user_data2 !== void 0 ? _response$user_data2 : \"\"));\n          localStorage.setItem('token', (_response$local_token2 = response.local_token) !== null && _response$local_token2 !== void 0 ? _response$local_token2 : \"\");\n          localStorage.setItem('ocrMode', 'STANDARD');\n          // Store the credentials in local storage\n          _this4.localCredentials = {\n            username: _this4.username,\n            password: _this4.password\n          };\n          localStorage.setItem('credentials', JSON.stringify(_this4.localCredentials));\n          const toast = yield _this4.toastController.create({\n            message: 'Login successful!',\n            duration: 2000,\n            color: 'success'\n          });\n          toast.present();\n          yield loading.dismiss();\n          if (!_this4.hasSeenOnboarding) {\n            yield _this4.storageService.set('hasSeenOnboarding', true);\n            _this4.navCtrl.navigateRoot('/guide');\n          } else {\n            _this4.navCtrl.navigateRoot('/scan-bl');\n          }\n        });\n        return function (_x4) {\n          return _ref4.apply(this, arguments);\n        };\n      }(), /*#__PURE__*/function () {\n        var _ref5 = _asyncToGenerator(function* (error) {\n          console.error('Pharmalien login error:', error);\n          yield loading.dismiss();\n        });\n        return function (_x5) {\n          return _ref5.apply(this, arguments);\n        };\n      }());\n    })();\n  }\n}\n_LoginPage = LoginPage;\n_LoginPage.ɵfac = function LoginPage_Factory(t) {\n  return new (t || _LoginPage)(i0.ɵɵdirectiveInject(i1.NavController), i0.ɵɵdirectiveInject(i2.ApiService), i0.ɵɵdirectiveInject(i1.ToastController), i0.ɵɵdirectiveInject(i1.LoadingController), i0.ɵɵdirectiveInject(i3.StorageService));\n};\n_LoginPage.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: _LoginPage,\n  selectors: [[\"app-login\"]],\n  decls: 19,\n  vars: 5,\n  consts: [[3, \"fullscreen\"], [1, \"login-wrapper\"], [\"size\", \"12\", 1, \"login-content\"], [4, \"ngIf\"], [\"fill\", \"clear\", 1, \"back-button\", 3, \"click\"], [\"name\", \"arrow-back-outline\", \"slot\", \"start\"], [\"src\", \"/assets/sophatel_logo.svg\", \"alt\", \"SOPHATEL Logo\", 1, \"logo\"], [1, \"copyright\", \"text-center\"], [1, \"step-indicators\"], [1, \"step-badge\", 3, \"ngClass\"], [1, \"step-number\"], [1, \"step-label\"], [1, \"step-line\"], [1, \"content-login-head\"], [\"class\", \"inputs-login\", 4, \"ngIf\"], [\"fill\", \"clear\", \"class\", \"back-button\", 3, \"click\", 4, \"ngIf\"], [1, \"inputs-login\"], [\"lines\", \"none\", 1, \"input-item\"], [\"type\", \"text\", \"placeholder\", \"Identifiant de la pharmacie\", \"required\", \"\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"password\", \"placeholder\", \"Mot de passe de la pharmacie\", \"required\", \"\", 3, \"ngModelChange\", \"ngModel\"], [\"expand\", \"block\", \"class\", \"login-button\", 3, \"click\", 4, \"ngIf\"], [\"expand\", \"block\", 1, \"login-button\", 3, \"click\"], [\"type\", \"text\", \"placeholder\", \"Identifiant de l'utilisateur\", \"required\", \"\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"password\", \"placeholder\", \"Mot de passe de l'utilisateur\", \"required\", \"\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"text\", \"placeholder\", \"Identifiant utilisateur\", \"required\", \"\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"password\", \"placeholder\", \"Mot de passe\", \"required\", \"\", 3, \"ngModelChange\", \"ngModel\"]],\n  template: function LoginPage_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelement(0, \"ion-header\");\n      i0.ɵɵelementStart(1, \"ion-content\", 0)(2, \"div\", 1)(3, \"ion-row\")(4, \"ion-col\", 2);\n      i0.ɵɵtemplate(5, LoginPage_div_5_Template, 20, 7, \"div\", 3)(6, LoginPage_div_6_Template, 8, 0, \"div\", 3)(7, LoginPage_div_7_Template, 4, 3, \"div\", 3)(8, LoginPage_div_8_Template, 8, 2, \"div\", 3);\n      i0.ɵɵelementStart(9, \"ion-button\", 4);\n      i0.ɵɵlistener(\"click\", function LoginPage_Template_ion_button_click_9_listener() {\n        return ctx.goBackToWelcome();\n      });\n      i0.ɵɵelement(10, \"ion-icon\", 5);\n      i0.ɵɵtext(11, \" Changer de plateforme \");\n      i0.ɵɵelementEnd()()()()();\n      i0.ɵɵelementStart(12, \"ion-footer\");\n      i0.ɵɵelement(13, \"img\", 6);\n      i0.ɵɵelementStart(14, \"p\", 7)(15, \"span\");\n      i0.ɵɵtext(16, \" Sophatel Ing\\u00E9nierie \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(17, \"br\");\n      i0.ɵɵtext(18, \"WinDoc \\u00A9 2025 - Tous droits r\\u00E9serv\\u00E9s.\");\n      i0.ɵɵelementEnd()();\n    }\n    if (rf & 2) {\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"fullscreen\", true);\n      i0.ɵɵadvance(4);\n      i0.ɵɵproperty(\"ngIf\", !ctx.isPharmalienPlatform);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", ctx.isPharmalienPlatform);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", !ctx.isPharmalienPlatform);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", ctx.isPharmalienPlatform);\n    }\n  },\n  dependencies: [i4.NgClass, i4.NgIf, i5.NgControlStatus, i5.RequiredValidator, i5.NgModel, i1.IonButton, i1.IonCol, i1.IonContent, i1.IonFooter, i1.IonHeader, i1.IonIcon, i1.IonInput, i1.IonItem, i1.IonRow, i1.TextValueAccessor],\n  styles: [\"@import url(https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap);@import url(https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0[_ngcontent-%COMP%], 200[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 300[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 400[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 500[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 600[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 700[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 800[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 900[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 100[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 200[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 300[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 400[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 500[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 600[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 700[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 800[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 900&display=swap)[_ngcontent-%COMP%];*[_ngcontent-%COMP%] {\\n  font-family: \\\"Inter\\\", sans-serif;\\n  font-optical-sizing: auto;\\n}\\n\\nion-content[_ngcontent-%COMP%]::part(scroll) {\\n  scrollbar-width: none !important;\\n  -ms-overflow-style: none !important;\\n}\\n\\n.login-wrapper[_ngcontent-%COMP%] {\\n  background: url(\\\"/assets/bg-welcome.png\\\") no-repeat center center fixed;\\n  background-size: cover;\\n  min-height: 100vh;\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: center;\\n  align-items: center;\\n  position: relative;\\n  padding: 20px;\\n  padding-bottom: 120px;\\n}\\n\\n.content-login-head[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  flex-direction: column;\\n  margin-bottom: 30px;\\n}\\n\\n.content-login-head[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  color: #1f41bb;\\n  font-size: 34px;\\n  font-weight: bold;\\n  text-shadow: 0px 0px 1.5px #1f41bb;\\n  text-align: center;\\n}\\n\\n.content-login-head[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #141414;\\n  font-size: 18px;\\n  font-weight: 600;\\n  text-align: center;\\n  text-shadow: 0px 0px 0.5px #050505;\\n}\\n\\nion-toolbar[_ngcontent-%COMP%] {\\n  --background: transparent;\\n  --ion-color-primary: #2f4fcd;\\n}\\n\\nion-button.login-button[_ngcontent-%COMP%] {\\n  --background: #1f41bb;\\n  --background-activated: #1f41bb;\\n  --border-radius: 10px;\\n  text-align: center;\\n  box-shadow: 0px 16px 20px rgb(203, 214, 255); \\n\\n  color: #fff;\\n  font-size: 20px;\\n  font-weight: bold;\\n}\\n\\nion-button[_ngcontent-%COMP%]::part(native) {\\n  --padding-top: 20px !important;\\n  --padding-bottom: 20px !important;\\n}\\n\\nion-row[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n}\\n\\n  ion-col {\\n  display: flex !important;\\n  flex-direction: column;\\n  justify-content: center;\\n  align-items: center;\\n  width: 100%;\\n  max-width: 400px;\\n}\\n\\n.inputs-login[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 30px;\\n  flex-direction: column;\\n  width: 85%;\\n  animation: _ngcontent-%COMP%_fadeIn 0.3s ease-in-out;\\n}\\n\\n  .login-content {\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: center;\\n  align-items: center;\\n  width: 100%;\\n  padding: 20px;\\n  gap: 20px;\\n}\\n\\n  .login-content ion-item {\\n  width: 100%;\\n  padding-right: 50px;\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: #050505;\\n  text-align: left;\\n  padding: 8px 10px;\\n  border: 2px solid #1f41bb;\\n  border-radius: 10px;\\n  --background: #f1f4ff; \\n\\n  background-color: #f1f4ff;\\n}\\n\\n.forgot-password[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding-right: 50px;\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: #1f41bb;\\n  text-align: right;\\n  text-decoration: none;\\n  margin-bottom: 20px;\\n}\\n\\nion-footer[_ngcontent-%COMP%] {\\n  position: fixed;\\n  bottom: 0;\\n  left: 0;\\n  width: 100%;\\n  background: rgba(255, 255, 255, 0.9);\\n  padding: 10px;\\n  z-index: 999;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  transition: transform 0.3s ease-out;\\n}\\nion-footer.keyboard-open[_ngcontent-%COMP%] {\\n  transform: translateY(100%);\\n}\\n\\n.copyright[_ngcontent-%COMP%] {\\n  color: #1f41bb;\\n  padding-top: 5px;\\n  font-weight: 500;\\n  font-size: 14px;\\n  text-shadow: 0px 0px 1px #1f41bb;\\n  text-align: center;\\n}\\n.copyright[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: bold;\\n}\\n\\n.step-indicators[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin-bottom: 30px;\\n  margin-top: 10px;\\n  width: 100%;\\n  max-width: 300px;\\n}\\n\\n.step-badge[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  position: relative;\\n  transition: all 0.3s ease;\\n}\\n.step-badge[_ngcontent-%COMP%]   .step-number[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  background-color: #f1f4ff;\\n  border: 2px solid #1f41bb;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: #1f41bb;\\n  font-weight: bold;\\n  font-size: 18px;\\n  transition: all 0.3s ease;\\n}\\n.step-badge[_ngcontent-%COMP%]   .step-label[_ngcontent-%COMP%] {\\n  margin-top: 8px;\\n  color: #1f41bb;\\n  font-size: 14px;\\n  font-weight: 500;\\n}\\n.step-badge[_ngcontent-%COMP%]:hover {\\n  cursor: pointer;\\n  transform: translateY(-2px);\\n}\\n.step-badge.active[_ngcontent-%COMP%]   .step-number[_ngcontent-%COMP%] {\\n  background-color: #1f41bb;\\n  color: white;\\n  transform: scale(1.1);\\n}\\n.step-badge.active[_ngcontent-%COMP%]   .step-label[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n}\\n\\n.step-line[_ngcontent-%COMP%] {\\n  flex: 1;\\n  height: 2px;\\n  background-color: #1f41bb;\\n  margin: 0 15px;\\n  margin-bottom: 20px;\\n  max-width: 100px;\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from {\\n    opacity: 0;\\n    transform: translateY(10px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvbG9naW4vbG9naW4ucGFnZS5zY3NzIiwid2VicGFjazovLy4vLi4vLi4vLi4vLi4vV29yayUyMF9fQWJkZXJyYWhtYW5lX291aG5hL09DUl9ET0NVTUVOVF9HUk9TU0lTVEUvRnJvbnRlbmQlMjBvY3IlMjBncm9zc2lzdGUlMjBkb2N1bWVudC9mcm9udGVuZF9vY3JfZ3Jvc3Npc3RlX2RvY3VtZW50L3NyYy9hcHAvbG9naW4vbG9naW4ucGFnZS5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUdBO0VBQ0UsZ0NBQUE7RUFDQSx5QkFBQTtBQ0FGOztBREdBO0VBR0UsZ0NBQUE7RUFDQSxtQ0FBQTtBQ0ZGOztBREtBO0VBQ0UsdUVBQUE7RUFDQSxzQkFBQTtFQUNBLGlCQUFBO0VBQ0EsYUFBQTtFQUNBLHNCQUFBO0VBQ0EsdUJBQUE7RUFDQSxtQkFBQTtFQUNBLGtCQUFBO0VBQ0EsYUFBQTtFQUNBLHFCQUFBO0FDRkY7O0FES0E7RUFDRSxhQUFBO0VBQ0EsdUJBQUE7RUFDQSxtQkFBQTtFQUNBLHNCQUFBO0VBQ0EsbUJBQUE7QUNGRjs7QURLQTtFQUNFLGNBQUE7RUFDQSxlQUFBO0VBQ0EsaUJBQUE7RUFDQSxrQ0FBQTtFQUNBLGtCQUFBO0FDRkY7O0FESUE7RUFDRSxjQUFBO0VBQ0EsZUFBQTtFQUNBLGdCQUFBO0VBQ0Esa0JBQUE7RUFDQSxrQ0FBQTtBQ0RGOztBRElBO0VBQ0UseUJBQUE7RUFDQSw0QkFBQTtBQ0RGOztBRElBO0VBQ0UscUJBQUE7RUFDQSwrQkFBQTtFQUNBLHFCQUFBO0VBRUEsa0JBQUE7RUFDQSw0Q0FBQSxFQUFBLGVBQUE7RUFDQSxXQUFBO0VBQ0EsZUFBQTtFQUNBLGlCQUFBO0FDRkY7O0FES0E7RUFDRSw4QkFBQTtFQUNBLGlDQUFBO0FDRkY7O0FESUE7RUFDRSxXQUFBO0VBQ0EsWUFBQTtFQUNBLGFBQUE7RUFDQSx1QkFBQTtFQUNBLG1CQUFBO0FDREY7O0FESUE7RUFDRSx3QkFBQTtFQUNBLHNCQUFBO0VBQ0EsdUJBQUE7RUFDQSxtQkFBQTtFQUNBLFdBQUE7RUFDQSxnQkFBQTtBQ0RGOztBRElBO0VBQ0UsYUFBQTtFQUNBLFNBQUE7RUFDQSxzQkFBQTtFQUNBLFVBQUE7RUFPQSxrQ0FBQTtBQ1BGOztBRFVBO0VBQ0UsYUFBQTtFQUNBLHNCQUFBO0VBQ0EsdUJBQUE7RUFDQSxtQkFBQTtFQUNBLFdBQUE7RUFDQSxhQUFBO0VBQ0EsU0FBQTtBQ1BGOztBRFVBO0VBQ0UsV0FBQTtFQUNBLG1CQUFBO0VBQ0EsZUFBQTtFQUNBLGdCQUFBO0VBQ0EsY0FBQTtFQUNBLGdCQUFBO0VBRUEsaUJBQUE7RUFDQSx5QkFBQTtFQUNBLG1CQUFBO0VBQ0EscUJBQUEsRUFBQSxzQ0FBQTtFQUNBLHlCQUFBO0FDUkY7O0FEYUE7RUFDRSxXQUFBO0VBQ0EsbUJBQUE7RUFDQSxlQUFBO0VBQ0EsZ0JBQUE7RUFDQSxjQUFBO0VBQ0EsaUJBQUE7RUFDQSxxQkFBQTtFQUNBLG1CQUFBO0FDVkY7O0FEYUE7RUFDRSxlQUFBO0VBQ0EsU0FBQTtFQUNBLE9BQUE7RUFDQSxXQUFBO0VBQ0Esb0NBQUE7RUFDQSxhQUFBO0VBQ0EsWUFBQTtFQUNBLGFBQUE7RUFDQSxzQkFBQTtFQUNBLG1CQUFBO0VBR0EsbUNBQUE7QUNaRjtBRGNFO0VBQ0UsMkJBQUE7QUNaSjs7QURnQkE7RUFDRSxjQUFBO0VBQ0EsZ0JBQUE7RUFDQSxnQkFBQTtFQUNBLGVBQUE7RUFDQSxnQ0FBQTtFQUNBLGtCQUFBO0FDYkY7QURjRTtFQUNFLGVBQUE7RUFDQSxpQkFBQTtBQ1pKOztBRGlCQTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLHVCQUFBO0VBQ0EsbUJBQUE7RUFDQSxnQkFBQTtFQUNBLFdBQUE7RUFDQSxnQkFBQTtBQ2RGOztBRG1CQTtFQUNFLGFBQUE7RUFDQSxzQkFBQTtFQUNBLG1CQUFBO0VBQ0Esa0JBQUE7RUFDQSx5QkFBQTtBQ2hCRjtBRGtCRTtFQUNFLFdBQUE7RUFDQSxZQUFBO0VBQ0Esa0JBQUE7RUFDQSx5QkFBQTtFQUNBLHlCQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsdUJBQUE7RUFDQSxjQUFBO0VBQ0EsaUJBQUE7RUFDQSxlQUFBO0VBQ0EseUJBQUE7QUNoQko7QURtQkU7RUFDRSxlQUFBO0VBQ0EsY0FBQTtFQUNBLGVBQUE7RUFDQSxnQkFBQTtBQ2pCSjtBRG9CRTtFQUNFLGVBQUE7RUFDQSwyQkFBQTtBQ2xCSjtBRHNCSTtFQUNFLHlCQUFBO0VBQ0EsWUFBQTtFQUNBLHFCQUFBO0FDcEJOO0FEc0JJO0VBQ0UsZ0JBQUE7QUNwQk47O0FEeUJBO0VBQ0UsT0FBQTtFQUNBLFdBQUE7RUFDQSx5QkFBQTtFQUNBLGNBQUE7RUFDQSxtQkFBQTtFQUNBLGdCQUFBO0FDdEJGOztBRHlCQTtFQUNFO0lBQ0UsVUFBQTtJQUNBLDJCQUFBO0VDdEJGO0VEd0JBO0lBQ0UsVUFBQTtJQUNBLHdCQUFBO0VDdEJGO0FBQ0YiLCJzb3VyY2VzQ29udGVudCI6WyJAaW1wb3J0IHVybChcImh0dHBzOi8vZm9udHMuZ29vZ2xlYXBpcy5jb20vY3NzMj9mYW1pbHk9SW50ZXI6d2dodEAxMDAuLjkwMCZkaXNwbGF5PXN3YXBcIik7XHJcbkBpbXBvcnQgdXJsKFwiaHR0cHM6Ly9mb250cy5nb29nbGVhcGlzLmNvbS9jc3MyP2ZhbWlseT1Qb3BwaW5zOml0YWwsd2dodEAwLDEwMDswLDIwMDswLDMwMDswLDQwMDswLDUwMDswLDYwMDswLDcwMDswLDgwMDswLDkwMDsxLDEwMDsxLDIwMDsxLDMwMDsxLDQwMDsxLDUwMDsxLDYwMDsxLDcwMDsxLDgwMDsxLDkwMCZkaXNwbGF5PXN3YXBcIik7XHJcblxyXG4qIHtcclxuICBmb250LWZhbWlseTogXCJJbnRlclwiLCBzYW5zLXNlcmlmO1xyXG4gIGZvbnQtb3B0aWNhbC1zaXppbmc6IGF1dG87XHJcbn1cclxuXHJcbmlvbi1jb250ZW50OjpwYXJ0KHNjcm9sbCkge1xyXG4gIC8vIG92ZXJmbG93LXk6IGhpZGRlbiAhaW1wb3J0YW50O1xyXG4gIC8vIC0tb3ZlcmZsb3c6IGhpZGRlbiAhaW1wb3J0YW50O1xyXG4gIHNjcm9sbGJhci13aWR0aDogbm9uZSAhaW1wb3J0YW50O1xyXG4gIC1tcy1vdmVyZmxvdy1zdHlsZTogbm9uZSAhaW1wb3J0YW50O1xyXG59XHJcblxyXG4ubG9naW4td3JhcHBlciB7XHJcbiAgYmFja2dyb3VuZDogdXJsKFwiL2Fzc2V0cy9iZy13ZWxjb21lLnBuZ1wiKSBuby1yZXBlYXQgY2VudGVyIGNlbnRlciBmaXhlZDtcclxuICBiYWNrZ3JvdW5kLXNpemU6IGNvdmVyO1xyXG4gIG1pbi1oZWlnaHQ6IDEwMHZoO1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcclxuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcclxuICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gIHBvc2l0aW9uOiByZWxhdGl2ZTtcclxuICBwYWRkaW5nOiAyMHB4O1xyXG4gIHBhZGRpbmctYm90dG9tOiAxMjBweDsgLy8gQWRkIHBhZGRpbmcgZm9yIGZvb3RlclxyXG59XHJcblxyXG4uY29udGVudC1sb2dpbi1oZWFkIHtcclxuICBkaXNwbGF5OiBmbGV4O1xyXG4gIGp1c3RpZnktY29udGVudDogY2VudGVyO1xyXG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcclxuICBtYXJnaW4tYm90dG9tOiAzMHB4O1xyXG59XHJcblxyXG4uY29udGVudC1sb2dpbi1oZWFkIGgxIHtcclxuICBjb2xvcjogIzFmNDFiYjtcclxuICBmb250LXNpemU6IDM0cHg7XHJcbiAgZm9udC13ZWlnaHQ6IGJvbGQ7XHJcbiAgdGV4dC1zaGFkb3c6IDBweCAwcHggMS41cHggIzFmNDFiYjtcclxuICB0ZXh0LWFsaWduOiBjZW50ZXI7XHJcbn1cclxuLmNvbnRlbnQtbG9naW4taGVhZCBwIHtcclxuICBjb2xvcjogIzE0MTQxNDtcclxuICBmb250LXNpemU6IDE4cHg7XHJcbiAgZm9udC13ZWlnaHQ6IDYwMDtcclxuICB0ZXh0LWFsaWduOiBjZW50ZXI7XHJcbiAgdGV4dC1zaGFkb3c6IDBweCAwcHggMC41cHggIzA1MDUwNTtcclxufVxyXG5cclxuaW9uLXRvb2xiYXIge1xyXG4gIC0tYmFja2dyb3VuZDogdHJhbnNwYXJlbnQ7XHJcbiAgLS1pb24tY29sb3ItcHJpbWFyeTogIzJmNGZjZDtcclxufVxyXG5cclxuaW9uLWJ1dHRvbi5sb2dpbi1idXR0b24ge1xyXG4gIC0tYmFja2dyb3VuZDogIzFmNDFiYjtcclxuICAtLWJhY2tncm91bmQtYWN0aXZhdGVkOiAjMWY0MWJiO1xyXG4gIC0tYm9yZGVyLXJhZGl1czogMTBweDtcclxuICAvLyB3aWR0aDogODAlO1xyXG4gIHRleHQtYWxpZ246IGNlbnRlcjtcclxuICBib3gtc2hhZG93OiAwcHggMTZweCAyMHB4IHJnYmEoMjAzLCAyMTQsIDI1NSwgMSk7IC8qIEFkZCBzaGFkb3cgKi9cclxuICBjb2xvcjogI2ZmZjtcclxuICBmb250LXNpemU6IDIwcHg7XHJcbiAgZm9udC13ZWlnaHQ6IGJvbGQ7XHJcbn1cclxuXHJcbmlvbi1idXR0b246OnBhcnQobmF0aXZlKSB7XHJcbiAgLS1wYWRkaW5nLXRvcDogMjBweCAhaW1wb3J0YW50O1xyXG4gIC0tcGFkZGluZy1ib3R0b206IDIwcHggIWltcG9ydGFudDtcclxufVxyXG5pb24tcm93IHtcclxuICB3aWR0aDogMTAwJTtcclxuICBoZWlnaHQ6IDEwMCU7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcclxuICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG59XHJcblxyXG46Om5nLWRlZXAgaW9uLWNvbCB7XHJcbiAgZGlzcGxheTogZmxleCAhaW1wb3J0YW50O1xyXG4gIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XHJcbiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XHJcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICB3aWR0aDogMTAwJTtcclxuICBtYXgtd2lkdGg6IDQwMHB4O1xyXG59XHJcblxyXG4uaW5wdXRzLWxvZ2luIHtcclxuICBkaXNwbGF5OiBmbGV4O1xyXG4gIGdhcDogMzBweDtcclxuICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xyXG4gIHdpZHRoOiA4NSU7XHJcbiAgLy8gbWFyZ2luLWJvdHRvbTogMjBweDtcclxuICAvLyAgICAgZGlzcGxheTogZmxleDtcclxuICAvLyAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XHJcbiAgLy8gICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgLy8gICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XHJcbiAgLy8gICAgIG1hcmdpbjogMCA0MHB4O1xyXG4gIGFuaW1hdGlvbjogZmFkZUluIDAuM3MgZWFzZS1pbi1vdXQ7XHJcbn1cclxuXHJcbjo6bmctZGVlcCAubG9naW4tY29udGVudCB7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xyXG4gIGp1c3RpZnktY29udGVudDogY2VudGVyO1xyXG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgd2lkdGg6IDEwMCU7XHJcbiAgcGFkZGluZzogMjBweDtcclxuICBnYXA6IDIwcHg7XHJcbn1cclxuXHJcbjo6bmctZGVlcCAubG9naW4tY29udGVudCBpb24taXRlbSB7XHJcbiAgd2lkdGg6IDEwMCU7XHJcbiAgcGFkZGluZy1yaWdodDogNTBweDtcclxuICBmb250LXNpemU6IDE2cHg7XHJcbiAgZm9udC13ZWlnaHQ6IDYwMDtcclxuICBjb2xvcjogIzA1MDUwNTtcclxuICB0ZXh0LWFsaWduOiBsZWZ0O1xyXG4gIC8vIG1hcmdpbjogMTVweCAwO1xyXG4gIHBhZGRpbmc6IDhweCAxMHB4O1xyXG4gIGJvcmRlcjogMnB4IHNvbGlkICMxZjQxYmI7XHJcbiAgYm9yZGVyLXJhZGl1czogMTBweDtcclxuICAtLWJhY2tncm91bmQ6ICNmMWY0ZmY7IC8qIEJhY2tncm91bmQgY29sb3IgZm9yIGlucHV0IGZpZWxkcyAqL1xyXG4gIGJhY2tncm91bmQtY29sb3I6ICNmMWY0ZmY7XHJcbn1cclxuXHJcbi8vIC5sb2dpbi1jb250ZW50IGlvbi1pdGVtLCAubG9naW4tY29udGVudCBpb24taXRlbSBpb24taW5wdXRcclxuXHJcbi5mb3Jnb3QtcGFzc3dvcmQge1xyXG4gIHdpZHRoOiAxMDAlO1xyXG4gIHBhZGRpbmctcmlnaHQ6IDUwcHg7XHJcbiAgZm9udC1zaXplOiAxNnB4O1xyXG4gIGZvbnQtd2VpZ2h0OiA2MDA7XHJcbiAgY29sb3I6ICMxZjQxYmI7XHJcbiAgdGV4dC1hbGlnbjogcmlnaHQ7XHJcbiAgdGV4dC1kZWNvcmF0aW9uOiBub25lO1xyXG4gIG1hcmdpbi1ib3R0b206IDIwcHg7XHJcbn1cclxuXHJcbmlvbi1mb290ZXIge1xyXG4gIHBvc2l0aW9uOiBmaXhlZDsgLy8gQ2hhbmdlIHRvIGZpeGVkXHJcbiAgYm90dG9tOiAwO1xyXG4gIGxlZnQ6IDA7XHJcbiAgd2lkdGg6IDEwMCU7XHJcbiAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjkpOyAvLyBPcHRpb25hbDogYWRkIGJhY2tncm91bmRcclxuICBwYWRkaW5nOiAxMHB4O1xyXG4gIHotaW5kZXg6IDk5OTtcclxuICBkaXNwbGF5OiBmbGV4O1xyXG4gIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XHJcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuXHJcbiAgLy8gT3B0aW9uYWw6IGFkZCB0cmFuc2l0aW9uIGZvciBzbW9vdGggbW92ZW1lbnRcclxuICB0cmFuc2l0aW9uOiB0cmFuc2Zvcm0gMC4zcyBlYXNlLW91dDtcclxuXHJcbiAgJi5rZXlib2FyZC1vcGVuIHtcclxuICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgxMDAlKTsgLy8gSGlkZSBmb290ZXIgd2hlbiBrZXlib2FyZCBpcyBvcGVuXHJcbiAgfVxyXG59XHJcblxyXG4uY29weXJpZ2h0IHtcclxuICBjb2xvcjogIzFmNDFiYjtcclxuICBwYWRkaW5nLXRvcDogNXB4O1xyXG4gIGZvbnQtd2VpZ2h0OiA1MDA7XHJcbiAgZm9udC1zaXplOiAxNHB4O1xyXG4gIHRleHQtc2hhZG93OiAwcHggMHB4IDFweCAjMWY0MWJiO1xyXG4gIHRleHQtYWxpZ246IGNlbnRlcjtcclxuICBzcGFuIHtcclxuICAgIGZvbnQtc2l6ZTogMTZweDtcclxuICAgIGZvbnQtd2VpZ2h0OiBib2xkO1xyXG4gIH1cclxufVxyXG5cclxuLy8gU3RlcHMgbG9naW5cclxuLnN0ZXAtaW5kaWNhdG9ycyB7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gIGp1c3RpZnktY29udGVudDogY2VudGVyO1xyXG4gIG1hcmdpbi1ib3R0b206IDMwcHg7XHJcbiAgbWFyZ2luLXRvcDogMTBweDtcclxuICB3aWR0aDogMTAwJTtcclxuICBtYXgtd2lkdGg6IDMwMHB4O1xyXG4gIC8vIHBvc2l0aW9uOiBzdGlja3k7XHJcbiAgLy8gdG9wOiAwO1xyXG59XHJcblxyXG4uc3RlcC1iYWRnZSB7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xyXG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xyXG4gIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7XHJcblxyXG4gIC5zdGVwLW51bWJlciB7XHJcbiAgICB3aWR0aDogNDBweDtcclxuICAgIGhlaWdodDogNDBweDtcclxuICAgIGJvcmRlci1yYWRpdXM6IDUwJTtcclxuICAgIGJhY2tncm91bmQtY29sb3I6ICNmMWY0ZmY7XHJcbiAgICBib3JkZXI6IDJweCBzb2xpZCAjMWY0MWJiO1xyXG4gICAgZGlzcGxheTogZmxleDtcclxuICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcclxuICAgIGNvbG9yOiAjMWY0MWJiO1xyXG4gICAgZm9udC13ZWlnaHQ6IGJvbGQ7XHJcbiAgICBmb250LXNpemU6IDE4cHg7XHJcbiAgICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xyXG4gIH1cclxuXHJcbiAgLnN0ZXAtbGFiZWwge1xyXG4gICAgbWFyZ2luLXRvcDogOHB4O1xyXG4gICAgY29sb3I6ICMxZjQxYmI7XHJcbiAgICBmb250LXNpemU6IDE0cHg7XHJcbiAgICBmb250LXdlaWdodDogNTAwO1xyXG4gIH1cclxuXHJcbiAgJjpob3ZlciB7XHJcbiAgICBjdXJzb3I6IHBvaW50ZXI7XHJcbiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTJweCk7XHJcbiAgfVxyXG5cclxuICAmLmFjdGl2ZSB7XHJcbiAgICAuc3RlcC1udW1iZXIge1xyXG4gICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjMWY0MWJiO1xyXG4gICAgICBjb2xvcjogd2hpdGU7XHJcbiAgICAgIHRyYW5zZm9ybTogc2NhbGUoMS4xKTtcclxuICAgIH1cclxuICAgIC5zdGVwLWxhYmVsIHtcclxuICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcclxuICAgIH1cclxuICB9XHJcbn1cclxuXHJcbi5zdGVwLWxpbmUge1xyXG4gIGZsZXg6IDE7XHJcbiAgaGVpZ2h0OiAycHg7XHJcbiAgYmFja2dyb3VuZC1jb2xvcjogIzFmNDFiYjtcclxuICBtYXJnaW46IDAgMTVweDtcclxuICBtYXJnaW4tYm90dG9tOiAyMHB4O1xyXG4gIG1heC13aWR0aDogMTAwcHg7XHJcbn1cclxuXHJcbkBrZXlmcmFtZXMgZmFkZUluIHtcclxuICBmcm9tIHtcclxuICAgIG9wYWNpdHk6IDA7XHJcbiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoMTBweCk7XHJcbiAgfVxyXG4gIHRvIHtcclxuICAgIG9wYWNpdHk6IDE7XHJcbiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoMCk7XHJcbiAgfVxyXG59XHJcbiIsIkBpbXBvcnQgdXJsKFwiaHR0cHM6Ly9mb250cy5nb29nbGVhcGlzLmNvbS9jc3MyP2ZhbWlseT1JbnRlcjp3Z2h0QDEwMC4uOTAwJmRpc3BsYXk9c3dhcFwiKTtcbkBpbXBvcnQgdXJsKFwiaHR0cHM6Ly9mb250cy5nb29nbGVhcGlzLmNvbS9jc3MyP2ZhbWlseT1Qb3BwaW5zOml0YWwsd2dodEAwLDEwMDswLDIwMDswLDMwMDswLDQwMDswLDUwMDswLDYwMDswLDcwMDswLDgwMDswLDkwMDsxLDEwMDsxLDIwMDsxLDMwMDsxLDQwMDsxLDUwMDsxLDYwMDsxLDcwMDsxLDgwMDsxLDkwMCZkaXNwbGF5PXN3YXBcIik7XG4qIHtcbiAgZm9udC1mYW1pbHk6IFwiSW50ZXJcIiwgc2Fucy1zZXJpZjtcbiAgZm9udC1vcHRpY2FsLXNpemluZzogYXV0bztcbn1cblxuaW9uLWNvbnRlbnQ6OnBhcnQoc2Nyb2xsKSB7XG4gIHNjcm9sbGJhci13aWR0aDogbm9uZSAhaW1wb3J0YW50O1xuICAtbXMtb3ZlcmZsb3ctc3R5bGU6IG5vbmUgIWltcG9ydGFudDtcbn1cblxuLmxvZ2luLXdyYXBwZXIge1xuICBiYWNrZ3JvdW5kOiB1cmwoXCIvYXNzZXRzL2JnLXdlbGNvbWUucG5nXCIpIG5vLXJlcGVhdCBjZW50ZXIgY2VudGVyIGZpeGVkO1xuICBiYWNrZ3JvdW5kLXNpemU6IGNvdmVyO1xuICBtaW4taGVpZ2h0OiAxMDB2aDtcbiAgZGlzcGxheTogZmxleDtcbiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIHBvc2l0aW9uOiByZWxhdGl2ZTtcbiAgcGFkZGluZzogMjBweDtcbiAgcGFkZGluZy1ib3R0b206IDEyMHB4O1xufVxuXG4uY29udGVudC1sb2dpbi1oZWFkIHtcbiAgZGlzcGxheTogZmxleDtcbiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gIG1hcmdpbi1ib3R0b206IDMwcHg7XG59XG5cbi5jb250ZW50LWxvZ2luLWhlYWQgaDEge1xuICBjb2xvcjogIzFmNDFiYjtcbiAgZm9udC1zaXplOiAzNHB4O1xuICBmb250LXdlaWdodDogYm9sZDtcbiAgdGV4dC1zaGFkb3c6IDBweCAwcHggMS41cHggIzFmNDFiYjtcbiAgdGV4dC1hbGlnbjogY2VudGVyO1xufVxuXG4uY29udGVudC1sb2dpbi1oZWFkIHAge1xuICBjb2xvcjogIzE0MTQxNDtcbiAgZm9udC1zaXplOiAxOHB4O1xuICBmb250LXdlaWdodDogNjAwO1xuICB0ZXh0LWFsaWduOiBjZW50ZXI7XG4gIHRleHQtc2hhZG93OiAwcHggMHB4IDAuNXB4ICMwNTA1MDU7XG59XG5cbmlvbi10b29sYmFyIHtcbiAgLS1iYWNrZ3JvdW5kOiB0cmFuc3BhcmVudDtcbiAgLS1pb24tY29sb3ItcHJpbWFyeTogIzJmNGZjZDtcbn1cblxuaW9uLWJ1dHRvbi5sb2dpbi1idXR0b24ge1xuICAtLWJhY2tncm91bmQ6ICMxZjQxYmI7XG4gIC0tYmFja2dyb3VuZC1hY3RpdmF0ZWQ6ICMxZjQxYmI7XG4gIC0tYm9yZGVyLXJhZGl1czogMTBweDtcbiAgdGV4dC1hbGlnbjogY2VudGVyO1xuICBib3gtc2hhZG93OiAwcHggMTZweCAyMHB4IHJnYigyMDMsIDIxNCwgMjU1KTsgLyogQWRkIHNoYWRvdyAqL1xuICBjb2xvcjogI2ZmZjtcbiAgZm9udC1zaXplOiAyMHB4O1xuICBmb250LXdlaWdodDogYm9sZDtcbn1cblxuaW9uLWJ1dHRvbjo6cGFydChuYXRpdmUpIHtcbiAgLS1wYWRkaW5nLXRvcDogMjBweCAhaW1wb3J0YW50O1xuICAtLXBhZGRpbmctYm90dG9tOiAyMHB4ICFpbXBvcnRhbnQ7XG59XG5cbmlvbi1yb3cge1xuICB3aWR0aDogMTAwJTtcbiAgaGVpZ2h0OiAxMDAlO1xuICBkaXNwbGF5OiBmbGV4O1xuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbn1cblxuOjpuZy1kZWVwIGlvbi1jb2wge1xuICBkaXNwbGF5OiBmbGV4ICFpbXBvcnRhbnQ7XG4gIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICB3aWR0aDogMTAwJTtcbiAgbWF4LXdpZHRoOiA0MDBweDtcbn1cblxuLmlucHV0cy1sb2dpbiB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGdhcDogMzBweDtcbiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgd2lkdGg6IDg1JTtcbiAgYW5pbWF0aW9uOiBmYWRlSW4gMC4zcyBlYXNlLWluLW91dDtcbn1cblxuOjpuZy1kZWVwIC5sb2dpbi1jb250ZW50IHtcbiAgZGlzcGxheTogZmxleDtcbiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIHdpZHRoOiAxMDAlO1xuICBwYWRkaW5nOiAyMHB4O1xuICBnYXA6IDIwcHg7XG59XG5cbjo6bmctZGVlcCAubG9naW4tY29udGVudCBpb24taXRlbSB7XG4gIHdpZHRoOiAxMDAlO1xuICBwYWRkaW5nLXJpZ2h0OiA1MHB4O1xuICBmb250LXNpemU6IDE2cHg7XG4gIGZvbnQtd2VpZ2h0OiA2MDA7XG4gIGNvbG9yOiAjMDUwNTA1O1xuICB0ZXh0LWFsaWduOiBsZWZ0O1xuICBwYWRkaW5nOiA4cHggMTBweDtcbiAgYm9yZGVyOiAycHggc29saWQgIzFmNDFiYjtcbiAgYm9yZGVyLXJhZGl1czogMTBweDtcbiAgLS1iYWNrZ3JvdW5kOiAjZjFmNGZmOyAvKiBCYWNrZ3JvdW5kIGNvbG9yIGZvciBpbnB1dCBmaWVsZHMgKi9cbiAgYmFja2dyb3VuZC1jb2xvcjogI2YxZjRmZjtcbn1cblxuLmZvcmdvdC1wYXNzd29yZCB7XG4gIHdpZHRoOiAxMDAlO1xuICBwYWRkaW5nLXJpZ2h0OiA1MHB4O1xuICBmb250LXNpemU6IDE2cHg7XG4gIGZvbnQtd2VpZ2h0OiA2MDA7XG4gIGNvbG9yOiAjMWY0MWJiO1xuICB0ZXh0LWFsaWduOiByaWdodDtcbiAgdGV4dC1kZWNvcmF0aW9uOiBub25lO1xuICBtYXJnaW4tYm90dG9tOiAyMHB4O1xufVxuXG5pb24tZm9vdGVyIHtcbiAgcG9zaXRpb246IGZpeGVkO1xuICBib3R0b206IDA7XG4gIGxlZnQ6IDA7XG4gIHdpZHRoOiAxMDAlO1xuICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuOSk7XG4gIHBhZGRpbmc6IDEwcHg7XG4gIHotaW5kZXg6IDk5OTtcbiAgZGlzcGxheTogZmxleDtcbiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgdHJhbnNpdGlvbjogdHJhbnNmb3JtIDAuM3MgZWFzZS1vdXQ7XG59XG5pb24tZm9vdGVyLmtleWJvYXJkLW9wZW4ge1xuICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoMTAwJSk7XG59XG5cbi5jb3B5cmlnaHQge1xuICBjb2xvcjogIzFmNDFiYjtcbiAgcGFkZGluZy10b3A6IDVweDtcbiAgZm9udC13ZWlnaHQ6IDUwMDtcbiAgZm9udC1zaXplOiAxNHB4O1xuICB0ZXh0LXNoYWRvdzogMHB4IDBweCAxcHggIzFmNDFiYjtcbiAgdGV4dC1hbGlnbjogY2VudGVyO1xufVxuLmNvcHlyaWdodCBzcGFuIHtcbiAgZm9udC1zaXplOiAxNnB4O1xuICBmb250LXdlaWdodDogYm9sZDtcbn1cblxuLnN0ZXAtaW5kaWNhdG9ycyB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICBtYXJnaW4tYm90dG9tOiAzMHB4O1xuICBtYXJnaW4tdG9wOiAxMHB4O1xuICB3aWR0aDogMTAwJTtcbiAgbWF4LXdpZHRoOiAzMDBweDtcbn1cblxuLnN0ZXAtYmFkZ2Uge1xuICBkaXNwbGF5OiBmbGV4O1xuICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBwb3NpdGlvbjogcmVsYXRpdmU7XG4gIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7XG59XG4uc3RlcC1iYWRnZSAuc3RlcC1udW1iZXIge1xuICB3aWR0aDogNDBweDtcbiAgaGVpZ2h0OiA0MHB4O1xuICBib3JkZXItcmFkaXVzOiA1MCU7XG4gIGJhY2tncm91bmQtY29sb3I6ICNmMWY0ZmY7XG4gIGJvcmRlcjogMnB4IHNvbGlkICMxZjQxYmI7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICBjb2xvcjogIzFmNDFiYjtcbiAgZm9udC13ZWlnaHQ6IGJvbGQ7XG4gIGZvbnQtc2l6ZTogMThweDtcbiAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTtcbn1cbi5zdGVwLWJhZGdlIC5zdGVwLWxhYmVsIHtcbiAgbWFyZ2luLXRvcDogOHB4O1xuICBjb2xvcjogIzFmNDFiYjtcbiAgZm9udC1zaXplOiAxNHB4O1xuICBmb250LXdlaWdodDogNTAwO1xufVxuLnN0ZXAtYmFkZ2U6aG92ZXIge1xuICBjdXJzb3I6IHBvaW50ZXI7XG4gIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMnB4KTtcbn1cbi5zdGVwLWJhZGdlLmFjdGl2ZSAuc3RlcC1udW1iZXIge1xuICBiYWNrZ3JvdW5kLWNvbG9yOiAjMWY0MWJiO1xuICBjb2xvcjogd2hpdGU7XG4gIHRyYW5zZm9ybTogc2NhbGUoMS4xKTtcbn1cbi5zdGVwLWJhZGdlLmFjdGl2ZSAuc3RlcC1sYWJlbCB7XG4gIGZvbnQtd2VpZ2h0OiA2MDA7XG59XG5cbi5zdGVwLWxpbmUge1xuICBmbGV4OiAxO1xuICBoZWlnaHQ6IDJweDtcbiAgYmFja2dyb3VuZC1jb2xvcjogIzFmNDFiYjtcbiAgbWFyZ2luOiAwIDE1cHg7XG4gIG1hcmdpbi1ib3R0b206IDIwcHg7XG4gIG1heC13aWR0aDogMTAwcHg7XG59XG5cbkBrZXlmcmFtZXMgZmFkZUluIHtcbiAgZnJvbSB7XG4gICAgb3BhY2l0eTogMDtcbiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoMTBweCk7XG4gIH1cbiAgdG8ge1xuICAgIG9wYWNpdHk6IDE7XG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKDApO1xuICB9XG59Il0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n});", "map": {"version": 3, "names": ["environment", "Keyboard", "retry", "catchError", "of", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction1", "_c0", "ctx_r0", "showTenant<PERSON><PERSON>in", "ɵɵtextInterpolate", "ɵɵlistener", "LoginPage_div_7_div_1_ion_button_5_Template_ion_button_click_0_listener", "ɵɵrestoreView", "_r3", "ɵɵnextContext", "ɵɵresetView", "tenantLogin", "ɵɵtwoWayListener", "LoginPage_div_7_div_1_Template_ion_input_ngModelChange_2_listener", "$event", "_r2", "ɵɵtwoWayBindingSet", "tenantUsername", "LoginPage_div_7_div_1_Template_ion_input_ngModelChange_4_listener", "tenantPassword", "ɵɵtemplate", "LoginPage_div_7_div_1_ion_button_5_Template", "ɵɵtwoWayProperty", "LoginPage_div_7_div_2_ion_button_5_Template_ion_button_click_0_listener", "_r5", "userLogin", "LoginPage_div_7_div_2_Template_ion_input_ngModelChange_2_listener", "_r4", "username", "LoginPage_div_7_div_2_Template_ion_input_ngModelChange_4_listener", "password", "LoginPage_div_7_div_2_ion_button_5_Template", "LoginPage_div_7_ion_button_3_Template_ion_button_click_0_listener", "_r6", "goBackToTenantLogin", "LoginPage_div_7_div_1_Template", "LoginPage_div_7_div_2_Template", "LoginPage_div_7_ion_button_3_Template", "LoginPage_div_8_Template_ion_input_ngModelChange_3_listener", "_r7", "LoginPage_div_8_Template_ion_input_ngModelChange_5_listener", "LoginPage_div_8_Template_ion_button_click_6_listener", "pharmalien<PERSON><PERSON>in", "LoginPage", "constructor", "navCtrl", "apiService", "toastController", "loadingController", "storageService", "tenantToken", "hasSeenOnboarding", "localCredentials", "selectedPlatform", "isPharmalienPlatform", "ngOnInit", "_this", "_asyncToGenerator", "production", "JSON", "parse", "localStorage", "getItem", "addListener", "footer", "document", "querySelector", "classList", "add", "remove", "init", "get", "platform", "console", "log", "navigateRoot", "ngOnDestroy", "removeAllListeners", "_this2", "toast", "create", "message", "duration", "color", "then", "present", "loading", "tenantRequest", "pipe", "error", "status", "detail", "subscribe", "_ref", "response", "dismiss", "accessToken", "_x", "apply", "arguments", "_this3", "userRequest", "tenant_token", "_ref2", "_response$user_data", "_response$tenant_data", "_response$local_token", "setItem", "stringify", "user_data", "tenant_data", "local_token", "set", "_x2", "_ref3", "errorMessage", "replace", "_x3", "goBackToWelcome", "_this4", "pharmalienRequest", "_ref4", "_response$user_data2", "_response$local_token2", "_x4", "_ref5", "_x5", "ɵɵdirectiveInject", "i1", "NavController", "i2", "ApiService", "ToastController", "LoadingController", "i3", "StorageService", "selectors", "decls", "vars", "consts", "template", "LoginPage_Template", "rf", "ctx", "LoginPage_div_5_Template", "LoginPage_div_6_Template", "LoginPage_div_7_Template", "LoginPage_div_8_Template", "LoginPage_Template_ion_button_click_9_listener"], "sources": ["C:\\Users\\<USER>\\Downloads\\<PERSON> __<PERSON><PERSON><PERSON><PERSON><PERSON>_ouhna\\OCR_DOCUMENT_GROSSISTE\\Frontend ocr grossiste document\\frontend_ocr_grossiste_document\\src\\app\\login\\login.page.ts", "C:\\Users\\<USER>\\Downloads\\<PERSON> __<PERSON><PERSON><PERSON><PERSON><PERSON>_ouhna\\OCR_DOCUMENT_GROSSISTE\\Frontend ocr grossiste document\\frontend_ocr_grossiste_document\\src\\app\\login\\login.page.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { NavController, ToastController, LoadingController  } from '@ionic/angular';\r\nimport { ApiService } from '../services/api.service';\r\nimport { LoginRequest, LoginResponse, TenantLoginRequest, TenantLoginResponse, PharmalienLoginRequest, PharmalienLoginResponse } from '../../models/login';\r\nimport { environment } from '../../environments/environment';\r\nimport { Keyboard } from '@capacitor/keyboard';\r\nimport { retry, catchError } from 'rxjs/operators';\r\nimport { of } from 'rxjs';\r\nimport { StorageService } from '../services/storage.service';\r\n\r\n@Component({\r\n  selector: 'app-login',\r\n  templateUrl: './login.page.html',\r\n  styleUrls: ['./login.page.scss'],\r\n})\r\nexport class LoginPage implements OnInit {\r\n  tenantUsername: string = '';\r\n  tenantPassword: string = '';\r\n  username: string = '';\r\n  password: string = '';\r\n  showTenantLogin: boolean = true;\r\n  tenantToken: string = '';\r\n  hasSeenOnboarding: boolean = false;\r\n  localCredentials: any = {};\r\n  selectedPlatform: 'winpluspharma' | 'pharmalier' = 'winpluspharma';\r\n  isPharmalienPlatform = false;\r\n\r\n  constructor(\r\n    private navCtrl: NavController,\r\n    private apiService: ApiService,\r\n    private toastController: ToastController, \r\n    private loadingController: LoadingController,\r\n    private storageService: StorageService,\r\n  ) {}\r\n\r\n  async ngOnInit() {\r\n    if (environment.production) {\r\n      this.tenantUsername = '';\r\n      this.tenantPassword = '';\r\n      this.username = '';\r\n      this.password = '';\r\n    } else {\r\n      this.tenantUsername = '0001';\r\n      this.tenantPassword = '123456';\r\n      this.username = 'PH';\r\n      this.password = 'PH';\r\n    }\r\n\r\n\r\n    // Load the credentials from the local storage\r\n    this.localCredentials = await JSON.parse(localStorage.getItem('credentials') as any);  \r\n    if (this.localCredentials) {\r\n      this.tenantUsername = this.localCredentials.tenantUsername;\r\n      this.tenantPassword = this.localCredentials.tenantPassword\r\n      this.username = this.localCredentials.username;\r\n      this.password = this.localCredentials.password;\r\n    }\r\n\r\n    Keyboard.addListener('keyboardWillShow', () => {\r\n      const footer = document.querySelector('ion-footer');\r\n      footer?.classList.add('keyboard-open');\r\n    });\r\n\r\n    Keyboard.addListener('keyboardWillHide', () => {\r\n      const footer = document.querySelector('ion-footer');\r\n      footer?.classList.remove('keyboard-open');\r\n    });\r\n\r\n    // Initialize storage\r\n    await this.storageService.init();\r\n\r\n    // Check if the user has seen onboarding\r\n    this.hasSeenOnboarding = await this.storageService.get('hasSeenOnboarding');\r\n\r\n    // Detect selected platform\r\n    const platform = localStorage.getItem('src_app') as 'winpluspharma' | 'pharmalier';\r\n    if (platform) {\r\n      this.selectedPlatform = platform;\r\n      this.isPharmalienPlatform = platform === 'pharmalier';\r\n      console.log('Platform detected:', platform);\r\n    } else {\r\n      // If no platform selected, redirect back to welcome\r\n      this.navCtrl.navigateRoot('/welcome');\r\n    }\r\n  }\r\n\r\n\r\n  ngOnDestroy() {\r\n    Keyboard.removeAllListeners();\r\n  }\r\n\r\n  async tenantLogin() {\r\n\r\n    // Check if email and password are empty\r\n    if (this.tenantUsername === '' || this.tenantPassword === '') {\r\n      const toast = this.toastController.create({\r\n        message: 'Veuillez remplir les champs de connexion.',\r\n        duration: 2000,\r\n        color: 'danger',\r\n      }).then(toast => toast.present());\r\n      return;\r\n    }\r\n\r\n    const loading = await this.loadingController.create({\r\n      message: 'Connexion de la pharmacie...',\r\n    });\r\n    await loading.present();\r\n  \r\n    const tenantRequest: TenantLoginRequest = {\r\n      username: this.tenantUsername,\r\n      password: this.tenantPassword,\r\n    };\r\n  \r\n    this.apiService.tenantLogin(tenantRequest)\r\n      .pipe(\r\n        retry(1), // Retry the request once if it fails\r\n        catchError(error => {\r\n          console.log('Tenant login error:', error);\r\n          \r\n          if(error.status === 403){\r\n              const toast =  this.toastController.create({\r\n                message: error.error.detail,\r\n                duration: 2000,\r\n                color: 'danger',\r\n              }).then(toast => toast.present());\r\n          }\r\n          else{\r\n            const toast = this.toastController.create({\r\n              message: 'La connexion de la pharmacie a échoué. Veuillez réessayer.',\r\n              duration: 2000,\r\n              color: 'danger',\r\n            }).then(toast => toast.present());\r\n          }\r\n          console.error('Tenant login error:', error);\r\n          return of(null); // Return an observable with null to continue the stream\r\n        })\r\n      )\r\n      .subscribe(\r\n        async (response: TenantLoginResponse | null) => {\r\n          await loading.dismiss();\r\n          if (response) {\r\n            console.log('Tenant login response:', response);\r\n            this.tenantToken = response.accessToken;\r\n            this.showTenantLogin = false;\r\n            const toast = await this.toastController.create({\r\n              message: 'Pharmacie connectée avec succès !',\r\n              duration: 2000,\r\n              color: 'success',\r\n            });\r\n            toast.present();\r\n          }\r\n        }\r\n      );\r\n  }\r\n\r\n  async userLogin() {\r\n\r\n    // Check if email and password are empty\r\n    if (this.username === '' || this.password === '') {\r\n      const toast = this.toastController.create({\r\n        message: 'Veuillez remplir les champs de connexion.',\r\n        duration: 2000,\r\n        color: 'danger',\r\n      }).then(toast => toast.present());\r\n      return;\r\n    }\r\n\r\n    const loading = await this.loadingController.create({\r\n      message: 'Connexion ...',\r\n    });\r\n    await loading.present();\r\n\r\n    const userRequest: LoginRequest = {\r\n      username: this.username,\r\n      password: this.password,\r\n      tenant_token: this.tenantToken  // Include tenant token in the request\r\n    };\r\n\r\n    this.apiService.userLogin(userRequest, this.tenantToken).subscribe(\r\n      async (response: LoginResponse) => {\r\n        console.log('User login response:', response);\r\n        \r\n        // Store the user data in local storage\r\n        localStorage.setItem('tokenUser', JSON.stringify(response.user_data ?? \"\"));\r\n        localStorage.setItem('tokenTenant', JSON.stringify(response.tenant_data ?? \"\"));\r\n        localStorage.setItem('token', response.local_token ?? \"\");\r\n        localStorage.setItem('ocrMode', 'STANDARD');\r\n\r\n        // Store the credentials in local storage\r\n        this.localCredentials = {\r\n          tenantUsername: this.tenantUsername,\r\n          tenantPassword: this.tenantPassword,\r\n          username: this.username,\r\n          password: this.password,\r\n        };\r\n        localStorage.setItem('credentials', JSON.stringify(this.localCredentials));\r\n        \r\n        const toast = await this.toastController.create({\r\n          message: 'Login successful!',\r\n          duration: 2000,\r\n          color: 'success',\r\n        });\r\n        toast.present();\r\n        await loading.dismiss();\r\n\r\n        if (!this.hasSeenOnboarding) {\r\n          await this.storageService.set('hasSeenOnboarding', true);\r\n          this.navCtrl.navigateRoot('/guide');\r\n        }\r\n        else{\r\n          this.navCtrl.navigateRoot('/scan-bl');\r\n        }\r\n      },\r\n      async (error) => {\r\n        console.error('Login error:', error);\r\n      \r\n        // Extract the error message without \"Internal server error: 400:\"\r\n        let errorMessage = error.error.detail || 'Connexion échouée. Veuillez vérifier vos identifiants.';\r\n        \r\n        // Remove \"Internal server error: 400:\" pattern from the message\r\n        errorMessage = errorMessage.replace(/^Internal server error: \\d+: /, '');\r\n\r\n        const toast = await this.toastController.create({\r\n          message: errorMessage,\r\n          duration: 2000,\r\n          color: 'danger',\r\n        });\r\n        toast.present();\r\n        await loading.dismiss();\r\n      }\r\n    );\r\n  }\r\n\r\n  goBackToTenantLogin() {\r\n    this.showTenantLogin = true;\r\n    this.tenantToken = '';\r\n  }\r\n\r\n  goBackToWelcome() {\r\n    this.navCtrl.navigateRoot('/welcome');\r\n  }\r\n\r\n  async pharmalienLogin() {\r\n    // Check if username and password are empty\r\n    if (this.username === '' || this.password === '') {\r\n      const toast = this.toastController.create({\r\n        message: 'Veuillez remplir les champs de connexion.',\r\n        duration: 2000,\r\n        color: 'danger',\r\n      }).then(toast => toast.present());\r\n      return;\r\n    }\r\n\r\n    const loading = await this.loadingController.create({\r\n      message: 'Connexion ...',\r\n    });\r\n    await loading.present();\r\n\r\n    const pharmalienRequest: PharmalienLoginRequest = {\r\n      username: this.username,\r\n      password: this.password\r\n    };\r\n\r\n    this.apiService.pharmalienLogin(pharmalienRequest).subscribe(\r\n      async (response: PharmalienLoginResponse) => {\r\n        console.log('Pharmalien login response:', response);\r\n\r\n        // Store the user data in local storage for Pharmalien (single token system)\r\n        localStorage.setItem('tokenUser', JSON.stringify(response.user_data ?? \"\"));\r\n        localStorage.setItem('token', response.local_token ?? \"\");\r\n        localStorage.setItem('ocrMode', 'STANDARD');\r\n\r\n        // Store the credentials in local storage\r\n        this.localCredentials = {\r\n          username: this.username,\r\n          password: this.password,\r\n        };\r\n        localStorage.setItem('credentials', JSON.stringify(this.localCredentials));\r\n\r\n        const toast = await this.toastController.create({\r\n          message: 'Login successful!',\r\n          duration: 2000,\r\n          color: 'success',\r\n        });\r\n        toast.present();\r\n        await loading.dismiss();\r\n\r\n        if (!this.hasSeenOnboarding) {\r\n          await this.storageService.set('hasSeenOnboarding', true);\r\n          this.navCtrl.navigateRoot('/guide');\r\n        }\r\n        else{\r\n          this.navCtrl.navigateRoot('/scan-bl');\r\n        }\r\n      },\r\n      async (error) => {\r\n        console.error('Pharmalien login error:', error);\r\n        await loading.dismiss();\r\n      }\r\n    );\r\n  }\r\n}", "<ion-header>\r\n</ion-header>\r\n\r\n<ion-content [fullscreen]=\"true\">\r\n  <div class=\"login-wrapper\">\r\n    <ion-row>\r\n      <ion-col size=\"12\" class=\"login-content\">\r\n        <!-- WinPlus Platform (dual login) -->\r\n        <div *ngIf=\"!isPharmalienPlatform\">\r\n          <div class=\"step-indicators\">\r\n            <div class=\"step-badge\" [ngClass]=\"{'active': showTenantLogin}\">\r\n              <span class=\"step-number\">1</span>\r\n              <span class=\"step-label\">Pharmacie</span>\r\n            </div>\r\n            <div class=\"step-line\"></div>\r\n            <div class=\"step-badge\" [ngClass]=\"{'active': !showTenantLogin}\">\r\n              <span class=\"step-number\">2</span>\r\n              <span class=\"step-label\">Utilisateur</span>\r\n            </div>\r\n          </div>\r\n          <div class=\"content-login-head\">\r\n            <h1>{{ showTenantLogin ? 'Connexion de la pharmacie' : 'Connexion de l\\'utilisateur' }}</h1>\r\n            <p>Vous devez utiliser <br>les identifiants de WinPlusPharma</p>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Pharmalien Platform (single login) -->\r\n        <div *ngIf=\"isPharmalienPlatform\">\r\n          <div class=\"content-login-head\">\r\n            <h1>Connexion Pharmalien</h1>\r\n            <p>Vous devez utiliser <br>les identifiants de Pharmalien</p>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- WinPlus Platform Forms -->\r\n        <div *ngIf=\"!isPharmalienPlatform\">\r\n          <div class=\"inputs-login\" *ngIf=\"showTenantLogin\">\r\n            <ion-item lines=\"none\" class=\"input-item\">\r\n              <ion-input [(ngModel)]=\"tenantUsername\" type=\"text\" placeholder=\"Identifiant de la pharmacie\" required></ion-input>\r\n            </ion-item>\r\n\r\n            <ion-item lines=\"none\" class=\"input-item\">\r\n              <ion-input [(ngModel)]=\"tenantPassword\" type=\"password\" placeholder=\"Mot de passe de la pharmacie\" required></ion-input>\r\n            </ion-item>\r\n            <ion-button expand=\"block\" class=\"login-button\" (click)=\"tenantLogin()\" *ngIf=\"showTenantLogin\">Se connecter</ion-button>\r\n          </div>\r\n\r\n          <div class=\"inputs-login\" *ngIf=\"!showTenantLogin\">\r\n            <ion-item lines=\"none\" class=\"input-item\">\r\n              <ion-input [(ngModel)]=\"username\" type=\"text\" placeholder=\"Identifiant de l'utilisateur\" required></ion-input>\r\n            </ion-item>\r\n\r\n            <ion-item lines=\"none\" class=\"input-item\">\r\n              <ion-input [(ngModel)]=\"password\" type=\"password\" placeholder=\"Mot de passe de l'utilisateur\" required></ion-input>\r\n            </ion-item>\r\n            <ion-button expand=\"block\" class=\"login-button\" (click)=\"userLogin()\" *ngIf=\"!showTenantLogin\">Se connecter</ion-button>\r\n          </div>\r\n\r\n          <ion-button fill=\"clear\" class=\"back-button\" (click)=\"goBackToTenantLogin()\" *ngIf=\"!showTenantLogin\">\r\n            <ion-icon name=\"arrow-back-outline\" slot=\"start\"></ion-icon>\r\n            Retour à la connexion de la pharmacie\r\n          </ion-button>\r\n        </div>\r\n\r\n        <!-- Pharmalien Platform Form -->\r\n        <div *ngIf=\"isPharmalienPlatform\">\r\n          <div class=\"inputs-login\">\r\n            <ion-item lines=\"none\" class=\"input-item\">\r\n              <ion-input [(ngModel)]=\"username\" type=\"text\" placeholder=\"Identifiant utilisateur\" required></ion-input>\r\n            </ion-item>\r\n\r\n            <ion-item lines=\"none\" class=\"input-item\">\r\n              <ion-input [(ngModel)]=\"password\" type=\"password\" placeholder=\"Mot de passe\" required></ion-input>\r\n            </ion-item>\r\n            <ion-button expand=\"block\" class=\"login-button\" (click)=\"pharmalienLogin()\">Se connecter</ion-button>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Back to Welcome Button -->\r\n        <ion-button fill=\"clear\" class=\"back-button\" (click)=\"goBackToWelcome()\">\r\n          <ion-icon name=\"arrow-back-outline\" slot=\"start\"></ion-icon>\r\n          Changer de plateforme\r\n        </ion-button>\r\n      </ion-col>\r\n    </ion-row>\r\n\r\n\r\n  </div>\r\n</ion-content>\r\n<ion-footer>\r\n  <img src=\"/assets/sophatel_logo.svg\" alt=\"SOPHATEL Logo\" class=\"logo\">\r\n  <p class=\"copyright text-center\"><span> Sophatel Ingénierie </span> <br>WinDoc © 2025 - Tous droits réservés.</p> \r\n</ion-footer>"], "mappings": ";;AAIA,SAASA,WAAW,QAAQ,gCAAgC;AAC5D,SAASC,QAAQ,QAAQ,qBAAqB;AAC9C,SAASC,KAAK,EAAEC,UAAU,QAAQ,gBAAgB;AAClD,SAASC,EAAE,QAAQ,MAAM;;;;;;;;;;;;ICIXC,EAHN,CAAAC,cAAA,UAAmC,aACJ,aACqC,eACpC;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAClCH,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAE,MAAA,gBAAS;IACpCF,EADoC,CAAAG,YAAA,EAAO,EACrC;IACNH,EAAA,CAAAI,SAAA,cAA6B;IAE3BJ,EADF,CAAAC,cAAA,aAAiE,eACrC;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAClCH,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAE,MAAA,mBAAW;IAExCF,EAFwC,CAAAG,YAAA,EAAO,EACvC,EACF;IAEJH,EADF,CAAAC,cAAA,eAAgC,UAC1B;IAAAD,EAAA,CAAAE,MAAA,IAAmF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC5FH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAE,MAAA,4BAAoB;IAAAF,EAAA,CAAAI,SAAA,UAAI;IAAAJ,EAAA,CAAAE,MAAA,yCAAiC;IAEhEF,EAFgE,CAAAG,YAAA,EAAI,EAC5D,EACF;;;;IAdsBH,EAAA,CAAAK,SAAA,GAAuC;IAAvCL,EAAA,CAAAM,UAAA,YAAAN,EAAA,CAAAO,eAAA,IAAAC,GAAA,EAAAC,MAAA,CAAAC,eAAA,EAAuC;IAKvCV,EAAA,CAAAK,SAAA,GAAwC;IAAxCL,EAAA,CAAAM,UAAA,YAAAN,EAAA,CAAAO,eAAA,IAAAC,GAAA,GAAAC,MAAA,CAAAC,eAAA,EAAwC;IAM5DV,EAAA,CAAAK,SAAA,GAAmF;IAAnFL,EAAA,CAAAW,iBAAA,CAAAF,MAAA,CAAAC,eAAA,8DAAmF;;;;;IAQvFV,EAFJ,CAAAC,cAAA,UAAkC,cACA,SAC1B;IAAAD,EAAA,CAAAE,MAAA,2BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC7BH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,2BAAoB;IAAAF,EAAA,CAAAI,SAAA,SAAI;IAAAJ,EAAA,CAAAE,MAAA,qCAA8B;IAE7DF,EAF6D,CAAAG,YAAA,EAAI,EACzD,EACF;;;;;;IAYFH,EAAA,CAAAC,cAAA,qBAAgG;IAAhDD,EAAA,CAAAY,UAAA,mBAAAC,wEAAA;MAAAb,EAAA,CAAAc,aAAA,CAAAC,GAAA;MAAA,MAAAN,MAAA,GAAAT,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASR,MAAA,CAAAS,WAAA,EAAa;IAAA,EAAC;IAAyBlB,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;;;IANvHH,EAFJ,CAAAC,cAAA,cAAkD,mBACN,oBAC+D;IAA5FD,EAAA,CAAAmB,gBAAA,2BAAAC,kEAAAC,MAAA;MAAArB,EAAA,CAAAc,aAAA,CAAAQ,GAAA;MAAA,MAAAb,MAAA,GAAAT,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAuB,kBAAA,CAAAd,MAAA,CAAAe,cAAA,EAAAH,MAAA,MAAAZ,MAAA,CAAAe,cAAA,GAAAH,MAAA;MAAA,OAAArB,EAAA,CAAAiB,WAAA,CAAAI,MAAA;IAAA,EAA4B;IACzCrB,EADyG,CAAAG,YAAA,EAAY,EAC1G;IAGTH,EADF,CAAAC,cAAA,mBAA0C,oBACoE;IAAjGD,EAAA,CAAAmB,gBAAA,2BAAAM,kEAAAJ,MAAA;MAAArB,EAAA,CAAAc,aAAA,CAAAQ,GAAA;MAAA,MAAAb,MAAA,GAAAT,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAuB,kBAAA,CAAAd,MAAA,CAAAiB,cAAA,EAAAL,MAAA,MAAAZ,MAAA,CAAAiB,cAAA,GAAAL,MAAA;MAAA,OAAArB,EAAA,CAAAiB,WAAA,CAAAI,MAAA;IAAA,EAA4B;IACzCrB,EAD8G,CAAAG,YAAA,EAAY,EAC/G;IACXH,EAAA,CAAA2B,UAAA,IAAAC,2CAAA,yBAAgG;IAClG5B,EAAA,CAAAG,YAAA,EAAM;;;;IAPSH,EAAA,CAAAK,SAAA,GAA4B;IAA5BL,EAAA,CAAA6B,gBAAA,YAAApB,MAAA,CAAAe,cAAA,CAA4B;IAI5BxB,EAAA,CAAAK,SAAA,GAA4B;IAA5BL,EAAA,CAAA6B,gBAAA,YAAApB,MAAA,CAAAiB,cAAA,CAA4B;IAEgC1B,EAAA,CAAAK,SAAA,EAAqB;IAArBL,EAAA,CAAAM,UAAA,SAAAG,MAAA,CAAAC,eAAA,CAAqB;;;;;;IAW9FV,EAAA,CAAAC,cAAA,qBAA+F;IAA/CD,EAAA,CAAAY,UAAA,mBAAAkB,wEAAA;MAAA9B,EAAA,CAAAc,aAAA,CAAAiB,GAAA;MAAA,MAAAtB,MAAA,GAAAT,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASR,MAAA,CAAAuB,SAAA,EAAW;IAAA,EAAC;IAA0BhC,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;;;IANtHH,EAFJ,CAAAC,cAAA,cAAmD,mBACP,oBAC0D;IAAvFD,EAAA,CAAAmB,gBAAA,2BAAAc,kEAAAZ,MAAA;MAAArB,EAAA,CAAAc,aAAA,CAAAoB,GAAA;MAAA,MAAAzB,MAAA,GAAAT,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAuB,kBAAA,CAAAd,MAAA,CAAA0B,QAAA,EAAAd,MAAA,MAAAZ,MAAA,CAAA0B,QAAA,GAAAd,MAAA;MAAA,OAAArB,EAAA,CAAAiB,WAAA,CAAAI,MAAA;IAAA,EAAsB;IACnCrB,EADoG,CAAAG,YAAA,EAAY,EACrG;IAGTH,EADF,CAAAC,cAAA,mBAA0C,oBAC+D;IAA5FD,EAAA,CAAAmB,gBAAA,2BAAAiB,kEAAAf,MAAA;MAAArB,EAAA,CAAAc,aAAA,CAAAoB,GAAA;MAAA,MAAAzB,MAAA,GAAAT,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAuB,kBAAA,CAAAd,MAAA,CAAA4B,QAAA,EAAAhB,MAAA,MAAAZ,MAAA,CAAA4B,QAAA,GAAAhB,MAAA;MAAA,OAAArB,EAAA,CAAAiB,WAAA,CAAAI,MAAA;IAAA,EAAsB;IACnCrB,EADyG,CAAAG,YAAA,EAAY,EAC1G;IACXH,EAAA,CAAA2B,UAAA,IAAAW,2CAAA,yBAA+F;IACjGtC,EAAA,CAAAG,YAAA,EAAM;;;;IAPSH,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAA6B,gBAAA,YAAApB,MAAA,CAAA0B,QAAA,CAAsB;IAItBnC,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAA6B,gBAAA,YAAApB,MAAA,CAAA4B,QAAA,CAAsB;IAEoCrC,EAAA,CAAAK,SAAA,EAAsB;IAAtBL,EAAA,CAAAM,UAAA,UAAAG,MAAA,CAAAC,eAAA,CAAsB;;;;;;IAG/FV,EAAA,CAAAC,cAAA,oBAAsG;IAAzDD,EAAA,CAAAY,UAAA,mBAAA2B,kEAAA;MAAAvC,EAAA,CAAAc,aAAA,CAAA0B,GAAA;MAAA,MAAA/B,MAAA,GAAAT,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASR,MAAA,CAAAgC,mBAAA,EAAqB;IAAA,EAAC;IAC1EzC,EAAA,CAAAI,SAAA,kBAA4D;IAC5DJ,EAAA,CAAAE,MAAA,mDACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;;IA1BfH,EAAA,CAAAC,cAAA,UAAmC;IAuBjCD,EAtBA,CAAA2B,UAAA,IAAAe,8BAAA,kBAAkD,IAAAC,8BAAA,kBAWC,IAAAC,qCAAA,yBAWmD;IAIxG5C,EAAA,CAAAG,YAAA,EAAM;;;;IA1BuBH,EAAA,CAAAK,SAAA,EAAqB;IAArBL,EAAA,CAAAM,UAAA,SAAAG,MAAA,CAAAC,eAAA,CAAqB;IAWrBV,EAAA,CAAAK,SAAA,EAAsB;IAAtBL,EAAA,CAAAM,UAAA,UAAAG,MAAA,CAAAC,eAAA,CAAsB;IAW6BV,EAAA,CAAAK,SAAA,EAAsB;IAAtBL,EAAA,CAAAM,UAAA,UAAAG,MAAA,CAAAC,eAAA,CAAsB;;;;;;IAUhGV,EAHN,CAAAC,cAAA,UAAkC,cACN,mBACkB,oBACqD;IAAlFD,EAAA,CAAAmB,gBAAA,2BAAA0B,4DAAAxB,MAAA;MAAArB,EAAA,CAAAc,aAAA,CAAAgC,GAAA;MAAA,MAAArC,MAAA,GAAAT,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAuB,kBAAA,CAAAd,MAAA,CAAA0B,QAAA,EAAAd,MAAA,MAAAZ,MAAA,CAAA0B,QAAA,GAAAd,MAAA;MAAA,OAAArB,EAAA,CAAAiB,WAAA,CAAAI,MAAA;IAAA,EAAsB;IACnCrB,EAD+F,CAAAG,YAAA,EAAY,EAChG;IAGTH,EADF,CAAAC,cAAA,mBAA0C,oBAC8C;IAA3ED,EAAA,CAAAmB,gBAAA,2BAAA4B,4DAAA1B,MAAA;MAAArB,EAAA,CAAAc,aAAA,CAAAgC,GAAA;MAAA,MAAArC,MAAA,GAAAT,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAuB,kBAAA,CAAAd,MAAA,CAAA4B,QAAA,EAAAhB,MAAA,MAAAZ,MAAA,CAAA4B,QAAA,GAAAhB,MAAA;MAAA,OAAArB,EAAA,CAAAiB,WAAA,CAAAI,MAAA;IAAA,EAAsB;IACnCrB,EADwF,CAAAG,YAAA,EAAY,EACzF;IACXH,EAAA,CAAAC,cAAA,qBAA4E;IAA5BD,EAAA,CAAAY,UAAA,mBAAAoC,qDAAA;MAAAhD,EAAA,CAAAc,aAAA,CAAAgC,GAAA;MAAA,MAAArC,MAAA,GAAAT,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASR,MAAA,CAAAwC,eAAA,EAAiB;IAAA,EAAC;IAACjD,EAAA,CAAAE,MAAA,mBAAY;IAE5FF,EAF4F,CAAAG,YAAA,EAAa,EACjG,EACF;;;;IARWH,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAA6B,gBAAA,YAAApB,MAAA,CAAA0B,QAAA,CAAsB;IAItBnC,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAA6B,gBAAA,YAAApB,MAAA,CAAA4B,QAAA,CAAsB;;;ADzD/C,OAAM,MAAOa,SAAS;EAYpBC,YACUC,OAAsB,EACtBC,UAAsB,EACtBC,eAAgC,EAChCC,iBAAoC,EACpCC,cAA8B;IAJ9B,KAAAJ,OAAO,GAAPA,OAAO;IACP,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,cAAc,GAAdA,cAAc;IAhBxB,KAAAhC,cAAc,GAAW,EAAE;IAC3B,KAAAE,cAAc,GAAW,EAAE;IAC3B,KAAAS,QAAQ,GAAW,EAAE;IACrB,KAAAE,QAAQ,GAAW,EAAE;IACrB,KAAA3B,eAAe,GAAY,IAAI;IAC/B,KAAA+C,WAAW,GAAW,EAAE;IACxB,KAAAC,iBAAiB,GAAY,KAAK;IAClC,KAAAC,gBAAgB,GAAQ,EAAE;IAC1B,KAAAC,gBAAgB,GAAmC,eAAe;IAClE,KAAAC,oBAAoB,GAAG,KAAK;EAQzB;EAEGC,QAAQA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACZ,IAAIrE,WAAW,CAACsE,UAAU,EAAE;QAC1BF,KAAI,CAACvC,cAAc,GAAG,EAAE;QACxBuC,KAAI,CAACrC,cAAc,GAAG,EAAE;QACxBqC,KAAI,CAAC5B,QAAQ,GAAG,EAAE;QAClB4B,KAAI,CAAC1B,QAAQ,GAAG,EAAE;OACnB,MAAM;QACL0B,KAAI,CAACvC,cAAc,GAAG,MAAM;QAC5BuC,KAAI,CAACrC,cAAc,GAAG,QAAQ;QAC9BqC,KAAI,CAAC5B,QAAQ,GAAG,IAAI;QACpB4B,KAAI,CAAC1B,QAAQ,GAAG,IAAI;;MAItB;MACA0B,KAAI,CAACJ,gBAAgB,SAASO,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAQ,CAAC;MACpF,IAAIN,KAAI,CAACJ,gBAAgB,EAAE;QACzBI,KAAI,CAACvC,cAAc,GAAGuC,KAAI,CAACJ,gBAAgB,CAACnC,cAAc;QAC1DuC,KAAI,CAACrC,cAAc,GAAGqC,KAAI,CAACJ,gBAAgB,CAACjC,cAAc;QAC1DqC,KAAI,CAAC5B,QAAQ,GAAG4B,KAAI,CAACJ,gBAAgB,CAACxB,QAAQ;QAC9C4B,KAAI,CAAC1B,QAAQ,GAAG0B,KAAI,CAACJ,gBAAgB,CAACtB,QAAQ;;MAGhDzC,QAAQ,CAAC0E,WAAW,CAAC,kBAAkB,EAAE,MAAK;QAC5C,MAAMC,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,YAAY,CAAC;QACnDF,MAAM,aAANA,MAAM,eAANA,MAAM,CAAEG,SAAS,CAACC,GAAG,CAAC,eAAe,CAAC;MACxC,CAAC,CAAC;MAEF/E,QAAQ,CAAC0E,WAAW,CAAC,kBAAkB,EAAE,MAAK;QAC5C,MAAMC,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,YAAY,CAAC;QACnDF,MAAM,aAANA,MAAM,eAANA,MAAM,CAAEG,SAAS,CAACE,MAAM,CAAC,eAAe,CAAC;MAC3C,CAAC,CAAC;MAEF;MACA,MAAMb,KAAI,CAACP,cAAc,CAACqB,IAAI,EAAE;MAEhC;MACAd,KAAI,CAACL,iBAAiB,SAASK,KAAI,CAACP,cAAc,CAACsB,GAAG,CAAC,mBAAmB,CAAC;MAE3E;MACA,MAAMC,QAAQ,GAAGX,YAAY,CAACC,OAAO,CAAC,SAAS,CAAmC;MAClF,IAAIU,QAAQ,EAAE;QACZhB,KAAI,CAACH,gBAAgB,GAAGmB,QAAQ;QAChChB,KAAI,CAACF,oBAAoB,GAAGkB,QAAQ,KAAK,YAAY;QACrDC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEF,QAAQ,CAAC;OAC5C,MAAM;QACL;QACAhB,KAAI,CAACX,OAAO,CAAC8B,YAAY,CAAC,UAAU,CAAC;;IACtC;EACH;EAGAC,WAAWA,CAAA;IACTvF,QAAQ,CAACwF,kBAAkB,EAAE;EAC/B;EAEMlE,WAAWA,CAAA;IAAA,IAAAmE,MAAA;IAAA,OAAArB,iBAAA;MAEf;MACA,IAAIqB,MAAI,CAAC7D,cAAc,KAAK,EAAE,IAAI6D,MAAI,CAAC3D,cAAc,KAAK,EAAE,EAAE;QAC5D,MAAM4D,KAAK,GAAGD,MAAI,CAAC/B,eAAe,CAACiC,MAAM,CAAC;UACxCC,OAAO,EAAE,2CAA2C;UACpDC,QAAQ,EAAE,IAAI;UACdC,KAAK,EAAE;SACR,CAAC,CAACC,IAAI,CAACL,KAAK,IAAIA,KAAK,CAACM,OAAO,EAAE,CAAC;QACjC;;MAGF,MAAMC,OAAO,SAASR,MAAI,CAAC9B,iBAAiB,CAACgC,MAAM,CAAC;QAClDC,OAAO,EAAE;OACV,CAAC;MACF,MAAMK,OAAO,CAACD,OAAO,EAAE;MAEvB,MAAME,aAAa,GAAuB;QACxC3D,QAAQ,EAAEkD,MAAI,CAAC7D,cAAc;QAC7Ba,QAAQ,EAAEgD,MAAI,CAAC3D;OAChB;MAED2D,MAAI,CAAChC,UAAU,CAACnC,WAAW,CAAC4E,aAAa,CAAC,CACvCC,IAAI,CACHlG,KAAK,CAAC,CAAC,CAAC;MAAE;MACVC,UAAU,CAACkG,KAAK,IAAG;QACjBhB,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEe,KAAK,CAAC;QAEzC,IAAGA,KAAK,CAACC,MAAM,KAAK,GAAG,EAAC;UACpB,MAAMX,KAAK,GAAID,MAAI,CAAC/B,eAAe,CAACiC,MAAM,CAAC;YACzCC,OAAO,EAAEQ,KAAK,CAACA,KAAK,CAACE,MAAM;YAC3BT,QAAQ,EAAE,IAAI;YACdC,KAAK,EAAE;WACR,CAAC,CAACC,IAAI,CAACL,KAAK,IAAIA,KAAK,CAACM,OAAO,EAAE,CAAC;SACpC,MACG;UACF,MAAMN,KAAK,GAAGD,MAAI,CAAC/B,eAAe,CAACiC,MAAM,CAAC;YACxCC,OAAO,EAAE,4DAA4D;YACrEC,QAAQ,EAAE,IAAI;YACdC,KAAK,EAAE;WACR,CAAC,CAACC,IAAI,CAACL,KAAK,IAAIA,KAAK,CAACM,OAAO,EAAE,CAAC;;QAEnCZ,OAAO,CAACgB,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;QAC3C,OAAOjG,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CACH,CACAoG,SAAS;QAAA,IAAAC,IAAA,GAAApC,iBAAA,CACR,WAAOqC,QAAoC,EAAI;UAC7C,MAAMR,OAAO,CAACS,OAAO,EAAE;UACvB,IAAID,QAAQ,EAAE;YACZrB,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEoB,QAAQ,CAAC;YAC/ChB,MAAI,CAAC5B,WAAW,GAAG4C,QAAQ,CAACE,WAAW;YACvClB,MAAI,CAAC3E,eAAe,GAAG,KAAK;YAC5B,MAAM4E,KAAK,SAASD,MAAI,CAAC/B,eAAe,CAACiC,MAAM,CAAC;cAC9CC,OAAO,EAAE,mCAAmC;cAC5CC,QAAQ,EAAE,IAAI;cACdC,KAAK,EAAE;aACR,CAAC;YACFJ,KAAK,CAACM,OAAO,EAAE;;QAEnB,CAAC;QAAA,iBAAAY,EAAA;UAAA,OAAAJ,IAAA,CAAAK,KAAA,OAAAC,SAAA;QAAA;MAAA,IACF;IAAC;EACN;EAEM1E,SAASA,CAAA;IAAA,IAAA2E,MAAA;IAAA,OAAA3C,iBAAA;MAEb;MACA,IAAI2C,MAAI,CAACxE,QAAQ,KAAK,EAAE,IAAIwE,MAAI,CAACtE,QAAQ,KAAK,EAAE,EAAE;QAChD,MAAMiD,KAAK,GAAGqB,MAAI,CAACrD,eAAe,CAACiC,MAAM,CAAC;UACxCC,OAAO,EAAE,2CAA2C;UACpDC,QAAQ,EAAE,IAAI;UACdC,KAAK,EAAE;SACR,CAAC,CAACC,IAAI,CAACL,KAAK,IAAIA,KAAK,CAACM,OAAO,EAAE,CAAC;QACjC;;MAGF,MAAMC,OAAO,SAASc,MAAI,CAACpD,iBAAiB,CAACgC,MAAM,CAAC;QAClDC,OAAO,EAAE;OACV,CAAC;MACF,MAAMK,OAAO,CAACD,OAAO,EAAE;MAEvB,MAAMgB,WAAW,GAAiB;QAChCzE,QAAQ,EAAEwE,MAAI,CAACxE,QAAQ;QACvBE,QAAQ,EAAEsE,MAAI,CAACtE,QAAQ;QACvBwE,YAAY,EAAEF,MAAI,CAAClD,WAAW,CAAE;OACjC;MAEDkD,MAAI,CAACtD,UAAU,CAACrB,SAAS,CAAC4E,WAAW,EAAED,MAAI,CAAClD,WAAW,CAAC,CAAC0C,SAAS;QAAA,IAAAW,KAAA,GAAA9C,iBAAA,CAChE,WAAOqC,QAAuB,EAAI;UAAA,IAAAU,mBAAA,EAAAC,qBAAA,EAAAC,qBAAA;UAChCjC,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEoB,QAAQ,CAAC;UAE7C;UACAjC,YAAY,CAAC8C,OAAO,CAAC,WAAW,EAAEhD,IAAI,CAACiD,SAAS,EAAAJ,mBAAA,GAACV,QAAQ,CAACe,SAAS,cAAAL,mBAAA,cAAAA,mBAAA,GAAI,EAAE,CAAC,CAAC;UAC3E3C,YAAY,CAAC8C,OAAO,CAAC,aAAa,EAAEhD,IAAI,CAACiD,SAAS,EAAAH,qBAAA,GAACX,QAAQ,CAACgB,WAAW,cAAAL,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC,CAAC;UAC/E5C,YAAY,CAAC8C,OAAO,CAAC,OAAO,GAAAD,qBAAA,GAAEZ,QAAQ,CAACiB,WAAW,cAAAL,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;UACzD7C,YAAY,CAAC8C,OAAO,CAAC,SAAS,EAAE,UAAU,CAAC;UAE3C;UACAP,MAAI,CAAChD,gBAAgB,GAAG;YACtBnC,cAAc,EAAEmF,MAAI,CAACnF,cAAc;YACnCE,cAAc,EAAEiF,MAAI,CAACjF,cAAc;YACnCS,QAAQ,EAAEwE,MAAI,CAACxE,QAAQ;YACvBE,QAAQ,EAAEsE,MAAI,CAACtE;WAChB;UACD+B,YAAY,CAAC8C,OAAO,CAAC,aAAa,EAAEhD,IAAI,CAACiD,SAAS,CAACR,MAAI,CAAChD,gBAAgB,CAAC,CAAC;UAE1E,MAAM2B,KAAK,SAASqB,MAAI,CAACrD,eAAe,CAACiC,MAAM,CAAC;YAC9CC,OAAO,EAAE,mBAAmB;YAC5BC,QAAQ,EAAE,IAAI;YACdC,KAAK,EAAE;WACR,CAAC;UACFJ,KAAK,CAACM,OAAO,EAAE;UACf,MAAMC,OAAO,CAACS,OAAO,EAAE;UAEvB,IAAI,CAACK,MAAI,CAACjD,iBAAiB,EAAE;YAC3B,MAAMiD,MAAI,CAACnD,cAAc,CAAC+D,GAAG,CAAC,mBAAmB,EAAE,IAAI,CAAC;YACxDZ,MAAI,CAACvD,OAAO,CAAC8B,YAAY,CAAC,QAAQ,CAAC;WACpC,MACG;YACFyB,MAAI,CAACvD,OAAO,CAAC8B,YAAY,CAAC,UAAU,CAAC;;QAEzC,CAAC;QAAA,iBAAAsC,GAAA;UAAA,OAAAV,KAAA,CAAAL,KAAA,OAAAC,SAAA;QAAA;MAAA;QAAA,IAAAe,KAAA,GAAAzD,iBAAA,CACD,WAAOgC,KAAK,EAAI;UACdhB,OAAO,CAACgB,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;UAEpC;UACA,IAAI0B,YAAY,GAAG1B,KAAK,CAACA,KAAK,CAACE,MAAM,IAAI,wDAAwD;UAEjG;UACAwB,YAAY,GAAGA,YAAY,CAACC,OAAO,CAAC,+BAA+B,EAAE,EAAE,CAAC;UAExE,MAAMrC,KAAK,SAASqB,MAAI,CAACrD,eAAe,CAACiC,MAAM,CAAC;YAC9CC,OAAO,EAAEkC,YAAY;YACrBjC,QAAQ,EAAE,IAAI;YACdC,KAAK,EAAE;WACR,CAAC;UACFJ,KAAK,CAACM,OAAO,EAAE;UACf,MAAMC,OAAO,CAACS,OAAO,EAAE;QACzB,CAAC;QAAA,iBAAAsB,GAAA;UAAA,OAAAH,KAAA,CAAAhB,KAAA,OAAAC,SAAA;QAAA;MAAA,IACF;IAAC;EACJ;EAEAjE,mBAAmBA,CAAA;IACjB,IAAI,CAAC/B,eAAe,GAAG,IAAI;IAC3B,IAAI,CAAC+C,WAAW,GAAG,EAAE;EACvB;EAEAoE,eAAeA,CAAA;IACb,IAAI,CAACzE,OAAO,CAAC8B,YAAY,CAAC,UAAU,CAAC;EACvC;EAEMjC,eAAeA,CAAA;IAAA,IAAA6E,MAAA;IAAA,OAAA9D,iBAAA;MACnB;MACA,IAAI8D,MAAI,CAAC3F,QAAQ,KAAK,EAAE,IAAI2F,MAAI,CAACzF,QAAQ,KAAK,EAAE,EAAE;QAChD,MAAMiD,KAAK,GAAGwC,MAAI,CAACxE,eAAe,CAACiC,MAAM,CAAC;UACxCC,OAAO,EAAE,2CAA2C;UACpDC,QAAQ,EAAE,IAAI;UACdC,KAAK,EAAE;SACR,CAAC,CAACC,IAAI,CAACL,KAAK,IAAIA,KAAK,CAACM,OAAO,EAAE,CAAC;QACjC;;MAGF,MAAMC,OAAO,SAASiC,MAAI,CAACvE,iBAAiB,CAACgC,MAAM,CAAC;QAClDC,OAAO,EAAE;OACV,CAAC;MACF,MAAMK,OAAO,CAACD,OAAO,EAAE;MAEvB,MAAMmC,iBAAiB,GAA2B;QAChD5F,QAAQ,EAAE2F,MAAI,CAAC3F,QAAQ;QACvBE,QAAQ,EAAEyF,MAAI,CAACzF;OAChB;MAEDyF,MAAI,CAACzE,UAAU,CAACJ,eAAe,CAAC8E,iBAAiB,CAAC,CAAC5B,SAAS;QAAA,IAAA6B,KAAA,GAAAhE,iBAAA,CAC1D,WAAOqC,QAAiC,EAAI;UAAA,IAAA4B,oBAAA,EAAAC,sBAAA;UAC1ClD,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEoB,QAAQ,CAAC;UAEnD;UACAjC,YAAY,CAAC8C,OAAO,CAAC,WAAW,EAAEhD,IAAI,CAACiD,SAAS,EAAAc,oBAAA,GAAC5B,QAAQ,CAACe,SAAS,cAAAa,oBAAA,cAAAA,oBAAA,GAAI,EAAE,CAAC,CAAC;UAC3E7D,YAAY,CAAC8C,OAAO,CAAC,OAAO,GAAAgB,sBAAA,GAAE7B,QAAQ,CAACiB,WAAW,cAAAY,sBAAA,cAAAA,sBAAA,GAAI,EAAE,CAAC;UACzD9D,YAAY,CAAC8C,OAAO,CAAC,SAAS,EAAE,UAAU,CAAC;UAE3C;UACAY,MAAI,CAACnE,gBAAgB,GAAG;YACtBxB,QAAQ,EAAE2F,MAAI,CAAC3F,QAAQ;YACvBE,QAAQ,EAAEyF,MAAI,CAACzF;WAChB;UACD+B,YAAY,CAAC8C,OAAO,CAAC,aAAa,EAAEhD,IAAI,CAACiD,SAAS,CAACW,MAAI,CAACnE,gBAAgB,CAAC,CAAC;UAE1E,MAAM2B,KAAK,SAASwC,MAAI,CAACxE,eAAe,CAACiC,MAAM,CAAC;YAC9CC,OAAO,EAAE,mBAAmB;YAC5BC,QAAQ,EAAE,IAAI;YACdC,KAAK,EAAE;WACR,CAAC;UACFJ,KAAK,CAACM,OAAO,EAAE;UACf,MAAMC,OAAO,CAACS,OAAO,EAAE;UAEvB,IAAI,CAACwB,MAAI,CAACpE,iBAAiB,EAAE;YAC3B,MAAMoE,MAAI,CAACtE,cAAc,CAAC+D,GAAG,CAAC,mBAAmB,EAAE,IAAI,CAAC;YACxDO,MAAI,CAAC1E,OAAO,CAAC8B,YAAY,CAAC,QAAQ,CAAC;WACpC,MACG;YACF4C,MAAI,CAAC1E,OAAO,CAAC8B,YAAY,CAAC,UAAU,CAAC;;QAEzC,CAAC;QAAA,iBAAAiD,GAAA;UAAA,OAAAH,KAAA,CAAAvB,KAAA,OAAAC,SAAA;QAAA;MAAA;QAAA,IAAA0B,KAAA,GAAApE,iBAAA,CACD,WAAOgC,KAAK,EAAI;UACdhB,OAAO,CAACgB,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;UAC/C,MAAMH,OAAO,CAACS,OAAO,EAAE;QACzB,CAAC;QAAA,iBAAA+B,GAAA;UAAA,OAAAD,KAAA,CAAA3B,KAAA,OAAAC,SAAA;QAAA;MAAA,IACF;IAAC;EACJ;;aA7RWxD,SAAS;;mBAATA,UAAS,EAAAlD,EAAA,CAAAsI,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAAxI,EAAA,CAAAsI,iBAAA,CAAAG,EAAA,CAAAC,UAAA,GAAA1I,EAAA,CAAAsI,iBAAA,CAAAC,EAAA,CAAAI,eAAA,GAAA3I,EAAA,CAAAsI,iBAAA,CAAAC,EAAA,CAAAK,iBAAA,GAAA5I,EAAA,CAAAsI,iBAAA,CAAAO,EAAA,CAAAC,cAAA;AAAA;;QAAT5F,UAAS;EAAA6F,SAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,mBAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MCftBrJ,EAAA,CAAAI,SAAA,iBACa;MAKPJ,EAHN,CAAAC,cAAA,qBAAiC,aACJ,cAChB,iBACkC;MA2DvCD,EAzDA,CAAA2B,UAAA,IAAA4H,wBAAA,kBAAmC,IAAAC,wBAAA,iBAmBD,IAAAC,wBAAA,iBAQC,IAAAC,wBAAA,iBA8BD;MAclC1J,EAAA,CAAAC,cAAA,oBAAyE;MAA5BD,EAAA,CAAAY,UAAA,mBAAA+I,+CAAA;QAAA,OAASL,GAAA,CAAAzB,eAAA,EAAiB;MAAA,EAAC;MACtE7H,EAAA,CAAAI,SAAA,mBAA4D;MAC5DJ,EAAA,CAAAE,MAAA,+BACF;MAMRF,EANQ,CAAAG,YAAA,EAAa,EACL,EACF,EAGN,EACM;MACdH,EAAA,CAAAC,cAAA,kBAAY;MACVD,EAAA,CAAAI,SAAA,cAAsE;MACrCJ,EAAjC,CAAAC,cAAA,YAAiC,YAAM;MAACD,EAAA,CAAAE,MAAA,kCAAoB;MAAAF,EAAA,CAAAG,YAAA,EAAO;MAACH,EAAA,CAAAI,SAAA,UAAI;MAAAJ,EAAA,CAAAE,MAAA,4DAAqC;MAC/GF,EAD+G,CAAAG,YAAA,EAAI,EACtG;;;MAzFAH,EAAA,CAAAK,SAAA,EAAmB;MAAnBL,EAAA,CAAAM,UAAA,oBAAmB;MAKlBN,EAAA,CAAAK,SAAA,GAA2B;MAA3BL,EAAA,CAAAM,UAAA,UAAAgJ,GAAA,CAAAzF,oBAAA,CAA2B;MAmB3B7D,EAAA,CAAAK,SAAA,EAA0B;MAA1BL,EAAA,CAAAM,UAAA,SAAAgJ,GAAA,CAAAzF,oBAAA,CAA0B;MAQ1B7D,EAAA,CAAAK,SAAA,EAA2B;MAA3BL,EAAA,CAAAM,UAAA,UAAAgJ,GAAA,CAAAzF,oBAAA,CAA2B;MA8B3B7D,EAAA,CAAAK,SAAA,EAA0B;MAA1BL,EAAA,CAAAM,UAAA,SAAAgJ,GAAA,CAAAzF,oBAAA,CAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}