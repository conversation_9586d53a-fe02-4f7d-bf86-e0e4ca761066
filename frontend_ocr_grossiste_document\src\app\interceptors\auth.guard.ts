// auth.guard.ts
import { Injectable } from '@angular/core';
import { CanActivate, Router } from '@angular/router';
import { jwtDecode } from 'jwt-decode';
import * as moment from 'moment';

@Injectable({
  providedIn: 'root'
})
export class AuthGuard implements CanActivate {

  constructor(private router: Router) {}

  canActivate(): boolean {
    const tokenUser = localStorage.getItem('tokenUser');
    const tokenTenant = localStorage.getItem('tokenTenant');
    const tokenLocal = localStorage.getItem('token');
    const platform = localStorage.getItem('src_app');

    // Check if platform is selected
    if (!platform) {
      this.router.navigate(['/welcome']);
      return false;
    }

    if (platform === 'pharmalier') {
      // Pharmalien platform: only needs tokenUser and tokenLocal
      if (tokenUser && tokenLocal) {
        try {
          const isTokenUserValid = this.checkTokenExpiration(tokenUser);
          const isTokenLocalValid = this.checkTokenExpiration(tokenLocal);

          if (isTokenUserValid && isTokenLocalValid) {
            return true;
          }
        } catch (error) {
          console.error('Token validation error:', error);
        }
      }
    } else if (platform === 'winpluspharma') {
      // WinPlus platform: needs all three tokens
      if (tokenUser && tokenTenant && tokenLocal) {
        try {
          const isTokenUserValid = this.checkTokenExpiration(tokenUser);
          const isTokenTenantValid = this.checkTokenExpiration(tokenTenant);
          const isTokenLocalValid = this.checkTokenExpiration(tokenLocal);

          if (isTokenUserValid && isTokenTenantValid && isTokenLocalValid) {
            return true;
          }
        } catch (error) {
          console.error('Token validation error:', error);
        }
      }
    }

    // If any required token is missing or expired, redirect to login
    this.router.navigate(['/login']);
    return false;
  }

  private checkTokenExpiration(token: string): boolean {
    const decodedToken: any = jwtDecode(token);
    const expiration = moment(decodedToken?.exp * 1000);
    return moment(new Date()) < expiration;
  }
}