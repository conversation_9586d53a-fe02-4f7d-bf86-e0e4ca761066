#!/usr/bin/env python3
"""
Test script to verify src_app migration was successful

Usage:
    python scripts/test_src_app_migration.py [--env-file .env.local]
"""

import sys
import argparse
from pathlib import Path
from dotenv import load_dotenv

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.app.utils.db_operations import get_all_pre_bl_ocr, save_response_to_db
from src.app.database.connection import db_manager
from sqlalchemy import text
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_migration():
    """Test that src_app migration works correctly."""
    try:
        # Test 1: Check column exists
        with db_manager.postgres_engine.connect() as conn:
            result = conn.execute(text("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'pre_bl_ocr' AND column_name = 'src_app'
            """))
            if result.fetchone():
                logger.info("✅ src_app column exists")
            else:
                logger.error("❌ src_app column missing")
                return False
        
        # Test 2: Check default values
        with db_manager.postgres_engine.connect() as conn:
            result = conn.execute(text("""
                SELECT COUNT(*) FROM pre_bl_ocr WHERE src_app = 'winpluspharma'
            """))
            count = result.fetchone()[0]
            logger.info(f"✅ Records with default src_app: {count}")
        
        # Test 3: Test API functions work with src_app
        try:
            # Test get_all_pre_bl_ocr with src_app filter
            results = get_all_pre_bl_ocr("test_user", "test_tenant", "winpluspharma")
            logger.info(f"✅ get_all_pre_bl_ocr works with src_app filter")
        except Exception as e:
            logger.error(f"❌ get_all_pre_bl_ocr failed: {e}")
            return False
        
        logger.info("🎉 All migration tests passed!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Migration test failed: {e}")
        return False

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Test src_app migration')
    parser.add_argument('--env-file', default='.env.local', help='Environment file to load (default: .env.local)')
    args = parser.parse_args()

    # Load environment file
    env_file_path = project_root / args.env_file
    if env_file_path.exists():
        load_dotenv(env_file_path)
        logger.info(f"✅ Loaded environment from {args.env_file}")
    else:
        logger.error(f"❌ Environment file not found: {args.env_file}")
        sys.exit(1)

    if test_migration():
        print("✅ Migration verification successful!")
    else:
        print("❌ Migration verification failed!")
        sys.exit(1)
