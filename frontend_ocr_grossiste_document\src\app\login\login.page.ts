import { Component, OnInit } from '@angular/core';
import { NavController, ToastController, LoadingController  } from '@ionic/angular';
import { ApiService } from '../services/api.service';
import { LoginRequest, LoginResponse, TenantLoginRequest, TenantLoginResponse, PharmalienLoginRequest, PharmalienLoginResponse } from '../../models/login';
import { environment } from '../../environments/environment';
import { Keyboard } from '@capacitor/keyboard';
import { retry, catchError } from 'rxjs/operators';
import { of } from 'rxjs';
import { StorageService } from '../services/storage.service';

@Component({
  selector: 'app-login',
  templateUrl: './login.page.html',
  styleUrls: ['./login.page.scss'],
})
export class LoginPage implements OnInit {
  tenantUsername: string = '';
  tenantPassword: string = '';
  username: string = '';
  password: string = '';
  showTenantLogin: boolean = true;
  tenantToken: string = '';
  hasSeenOnboarding: boolean = false;
  localCredentials: any = {};
  selectedPlatform: 'winpluspharma' | 'pharmalien' = 'winpluspharma';
  isPharmalienPlatform = false;

  constructor(
    private navCtrl: NavController,
    private apiService: ApiService,
    private toastController: ToastController, 
    private loadingController: LoadingController,
    private storageService: StorageService,
  ) {}

  async ngOnInit() {
    if (environment.production) {
      this.tenantUsername = '';
      this.tenantPassword = '';
      this.username = '';
      this.password = '';
    } else {
      this.tenantUsername = '0001';
      this.tenantPassword = '123456';
      this.username = 'PH';
      this.password = 'PH';
    }


    Keyboard.addListener('keyboardWillShow', () => {
      const footer = document.querySelector('ion-footer');
      footer?.classList.add('keyboard-open');
    });

    Keyboard.addListener('keyboardWillHide', () => {
      const footer = document.querySelector('ion-footer');
      footer?.classList.remove('keyboard-open');
    });

    // Initialize storage
    await this.storageService.init();

    // Check if the user has seen onboarding
    this.hasSeenOnboarding = await this.storageService.get('hasSeenOnboarding');

    // Detect selected platform
    const platform = localStorage.getItem('src_app') as 'winpluspharma' | 'pharmalien';
    if (platform) {
      this.selectedPlatform = platform;
      this.isPharmalienPlatform = platform === 'pharmalien';
      console.log('Platform detected:', platform);

      // Load platform-specific credentials after platform detection
      await this.loadPlatformCredentials();
    } else {
      // If no platform selected, redirect back to welcome
      this.navCtrl.navigateRoot('/welcome');
    }
  }


  ngOnDestroy() {
    Keyboard.removeAllListeners();
  }

  async loadPlatformCredentials() {
    const credentialsKey = this.selectedPlatform === 'pharmalien' ? 'credentials_pharmalien' : 'credentials_winpluspharma';
    const storedCredentials = localStorage.getItem(credentialsKey);

    if (storedCredentials) {
      this.localCredentials = JSON.parse(storedCredentials);

      if (this.selectedPlatform === 'winpluspharma') {
        this.tenantUsername = this.localCredentials.tenantUsername || '';
        this.tenantPassword = this.localCredentials.tenantPassword || '';
      }
      this.username = this.localCredentials.username || '';
      this.password = this.localCredentials.password || '';

      console.log(`Loaded ${this.selectedPlatform} credentials:`, this.localCredentials);
    }
  }

  async tenantLogin() {

    // Check if email and password are empty
    if (this.tenantUsername === '' || this.tenantPassword === '') {
      const toast = this.toastController.create({
        message: 'Veuillez remplir les champs de connexion.',
        duration: 2000,
        color: 'danger',
      }).then(toast => toast.present());
      return;
    }

    const loading = await this.loadingController.create({
      message: 'Connexion de la pharmacie...',
    });
    await loading.present();
  
    const tenantRequest: TenantLoginRequest = {
      username: this.tenantUsername,
      password: this.tenantPassword,
    };
  
    this.apiService.tenantLogin(tenantRequest)
      .pipe(
        retry(1), // Retry the request once if it fails
        catchError(error => {
          console.log('Tenant login error:', error);
          
          if(error.status === 403){
              const toast =  this.toastController.create({
                message: error.error.detail,
                duration: 2000,
                color: 'danger',
              }).then(toast => toast.present());
          }
          else{
            const toast = this.toastController.create({
              message: 'La connexion de la pharmacie a échoué. Veuillez réessayer.',
              duration: 2000,
              color: 'danger',
            }).then(toast => toast.present());
          }
          console.error('Tenant login error:', error);
          return of(null); // Return an observable with null to continue the stream
        })
      )
      .subscribe(
        async (response: TenantLoginResponse | null) => {
          await loading.dismiss();
          if (response) {
            console.log('Tenant login response:', response);
            this.tenantToken = response.accessToken;
            this.showTenantLogin = false;
            const toast = await this.toastController.create({
              message: 'Pharmacie connectée avec succès !',
              duration: 2000,
              color: 'success',
            });
            toast.present();
          }
        }
      );
  }

  async userLogin() {

    // Check if email and password are empty
    if (this.username === '' || this.password === '') {
      const toast = this.toastController.create({
        message: 'Veuillez remplir les champs de connexion.',
        duration: 2000,
        color: 'danger',
      }).then(toast => toast.present());
      return;
    }

    const loading = await this.loadingController.create({
      message: 'Connexion ...',
    });
    await loading.present();

    const userRequest: LoginRequest = {
      username: this.username,
      password: this.password,
      tenant_token: this.tenantToken  // Include tenant token in the request
    };

    this.apiService.userLogin(userRequest, this.tenantToken).subscribe(
      async (response: LoginResponse) => {
        console.log('User login response:', response);
        
        // Store the user data in local storage
        localStorage.setItem('tokenUser', JSON.stringify(response.user_data ?? ""));
        localStorage.setItem('tokenTenant', JSON.stringify(response.tenant_data ?? ""));
        localStorage.setItem('token', response.local_token ?? "");
        localStorage.setItem('ocrMode', 'STANDARD');

        // Store the credentials in platform-specific local storage
        this.localCredentials = {
          tenantUsername: this.tenantUsername,
          tenantPassword: this.tenantPassword,
          username: this.username,
          password: this.password,
        };
        const credentialsKey = 'credentials_winpluspharma';
        localStorage.setItem(credentialsKey, JSON.stringify(this.localCredentials));
        
        const toast = await this.toastController.create({
          message: 'Login successful!',
          duration: 2000,
          color: 'success',
        });
        toast.present();
        await loading.dismiss();

        if (!this.hasSeenOnboarding) {
          await this.storageService.set('hasSeenOnboarding', true);
          this.navCtrl.navigateRoot('/guide');
        }
        else{
          this.navCtrl.navigateRoot('/scan-bl');
        }
      },
      async (error) => {
        console.error('Login error:', error);
      
        // Extract the error message without "Internal server error: 400:"
        let errorMessage = error.error.detail || 'Connexion échouée. Veuillez vérifier vos identifiants.';
        
        // Remove "Internal server error: 400:" pattern from the message
        errorMessage = errorMessage.replace(/^Internal server error: \d+: /, '');

        const toast = await this.toastController.create({
          message: errorMessage,
          duration: 2000,
          color: 'danger',
        });
        toast.present();
        await loading.dismiss();
      }
    );
  }

  goBackToTenantLogin() {
    this.showTenantLogin = true;
    this.tenantToken = '';
  }

  goBackToWelcome() {
    this.navCtrl.navigateRoot('/welcome');
  }

  async pharmalienLogin() {
    // Check if username and password are empty
    if (this.username === '' || this.password === '') {
      const toast = this.toastController.create({
        message: 'Veuillez remplir les champs de connexion.',
        duration: 2000,
        color: 'danger',
      }).then(toast => toast.present());
      return;
    }

    const loading = await this.loadingController.create({
      message: 'Connexion ...',
    });
    await loading.present();

    const pharmalienRequest: PharmalienLoginRequest = {
      username: this.username,
      password: this.password
    };

    this.apiService.pharmalienLogin(pharmalienRequest).subscribe(
      async (response: PharmalienLoginResponse) => {
        console.log('Pharmalien login response:', response);

        // Store the user data in local storage for Pharmalien (single token system)
        localStorage.setItem('tokenUser', JSON.stringify(response.user_data ?? ""));
        localStorage.setItem('token', response.local_token ?? "");
        localStorage.setItem('ocrMode', 'STANDARD');

        // Store the credentials in platform-specific local storage
        this.localCredentials = {
          username: this.username,
          password: this.password,
        };
        const credentialsKey = 'credentials_pharmalien';
        localStorage.setItem(credentialsKey, JSON.stringify(this.localCredentials));

        const toast = await this.toastController.create({
          message: 'Login successful!',
          duration: 2000,
          color: 'success',
        });
        toast.present();
        await loading.dismiss();

        if (!this.hasSeenOnboarding) {
          await this.storageService.set('hasSeenOnboarding', true);
          this.navCtrl.navigateRoot('/guide');
        }
        else{
          this.navCtrl.navigateRoot('/scan-bl');
        }
      },
      async (error) => {
        console.error('Pharmalien login error:', error);
        await loading.dismiss();
      }
    );
  }
}