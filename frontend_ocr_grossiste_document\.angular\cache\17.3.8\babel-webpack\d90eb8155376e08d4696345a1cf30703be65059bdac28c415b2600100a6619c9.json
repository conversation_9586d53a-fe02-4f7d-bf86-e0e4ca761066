{"ast": null, "code": "var _AuthInterceptor;\nimport { throwError } from 'rxjs';\nimport { jwtDecode } from 'jwt-decode';\nimport { catchError } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/api.service\";\nexport class AuthInterceptor {\n  constructor(apiService) {\n    this.apiService = apiService;\n  }\n  intercept(req, next) {\n    const tokenUser = localStorage.getItem('tokenUser');\n    const tokenTenant = localStorage.getItem('tokenTenant');\n    const tokenLocal = localStorage.getItem('token');\n    const platform = localStorage.getItem('src_app');\n    if (platform === 'pharmalier') {\n      // Pharmalien platform: only needs tokenUser and tokenLocal\n      if (tokenUser && tokenLocal) {\n        if (!this.apiService.isLoggedIn()) {\n          this.logout();\n          return throwError(() => new Error('Token has expired'));\n        }\n        const cloned = req.clone({\n          headers: req.headers.set('Authorization', `Bearer ${tokenLocal}`).set('AuthorizationUser', `BearerUser ${JSON.parse(tokenUser).accessToken}`)\n        });\n        return next.handle(cloned).pipe(catchError(error => {\n          if (error.status === 401 || error.status === 403) {\n            this.logout();\n          }\n          return throwError(() => error);\n        }));\n      }\n    } else if (platform === 'winpluspharma') {\n      // WinPlus platform: needs all three tokens\n      if (tokenUser && tokenTenant && tokenLocal) {\n        if (!this.apiService.isLoggedIn()) {\n          this.logout();\n          return throwError(() => new Error('Token has expired'));\n        }\n        const cloned = req.clone({\n          headers: req.headers.set('Authorization', `Bearer ${tokenLocal}`).set('AuthorizationTenant', `BearerTenant ${JSON.parse(tokenTenant).accessToken}`).set('AuthorizationUser', `BearerUser ${JSON.parse(tokenUser).accessToken}`)\n        });\n        return next.handle(cloned).pipe(catchError(error => {\n          if (error.status === 401 || error.status === 403) {\n            this.logout();\n          }\n          return throwError(() => error);\n        }));\n      }\n    }\n    return next.handle(req);\n  }\n  isTokenExpired(token) {\n    const decodedToken = jwtDecode(token);\n    const expirationDate = new Date(decodedToken.exp * 1000);\n    return expirationDate < new Date();\n  }\n  logout() {\n    localStorage.removeItem('tokenUser');\n    localStorage.removeItem('tokenTenant');\n    localStorage.removeItem('token');\n    localStorage.removeItem('credentials');\n    localStorage.removeItem('ocrMode');\n    // Keep src_app to remember platform choice\n    // Redirect the user to the login page or show a logout message\n  }\n}\n_AuthInterceptor = AuthInterceptor;\n_AuthInterceptor.ɵfac = function AuthInterceptor_Factory(t) {\n  return new (t || _AuthInterceptor)(i0.ɵɵinject(i1.ApiService));\n};\n_AuthInterceptor.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n  token: _AuthInterceptor,\n  factory: _AuthInterceptor.ɵfac\n});", "map": {"version": 3, "names": ["throwError", "jwtDecode", "catchError", "AuthInterceptor", "constructor", "apiService", "intercept", "req", "next", "tokenUser", "localStorage", "getItem", "tokenTenant", "tokenLocal", "platform", "isLoggedIn", "logout", "Error", "cloned", "clone", "headers", "set", "JSON", "parse", "accessToken", "handle", "pipe", "error", "status", "isTokenExpired", "token", "decodedToken", "expirationDate", "Date", "exp", "removeItem", "i0", "ɵɵinject", "i1", "ApiService", "factory", "ɵfac"], "sources": ["C:\\Users\\<USER>\\Downloads\\Work __<PERSON><PERSON><PERSON><PERSON><PERSON>_ouhna\\OCR_DOCUMENT_GROSSISTE\\Frontend ocr grossiste document\\frontend_ocr_grossiste_document\\src\\app\\interceptors\\auth.interceptor.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpEvent, HttpInterceptor, HttpHandler, HttpRequest } from '@angular/common/http';\r\nimport { Observable, throwError } from 'rxjs';\r\nimport { jwtDecode } from 'jwt-decode';\r\nimport { catchError } from 'rxjs/operators';\r\nimport { ApiService } from '../services/api.service';\r\n\r\n\r\ninterface DecodedToken {\r\n  exp: number;\r\n  iat: number;\r\n  sub: string;  // typically the user identifier\r\n  // Add other expected fields here\r\n}\r\n\r\n@Injectable()\r\nexport class AuthInterceptor implements HttpInterceptor {\r\n\r\n  constructor(private apiService: ApiService) { }\r\n\r\n  intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {\r\n    const tokenUser = localStorage.getItem('tokenUser');\r\n    const tokenTenant = localStorage.getItem('tokenTenant');\r\n    const tokenLocal = localStorage.getItem('token');\r\n    const platform = localStorage.getItem('src_app');\r\n\r\n    if (platform === 'pharmalier') {\r\n      // Pharmalien platform: only needs tokenUser and tokenLocal\r\n      if (tokenUser && tokenLocal) {\r\n        if (!this.apiService.isLoggedIn()) {\r\n          this.logout();\r\n          return throwError(() => new Error('Token has expired'));\r\n        }\r\n\r\n        const cloned = req.clone({\r\n          headers: req.headers\r\n            .set('Authorization', `Bearer ${tokenLocal}`)\r\n            .set('AuthorizationUser', `BearerUser ${JSON.parse(tokenUser).accessToken}`)\r\n        });\r\n        return next.handle(cloned).pipe(\r\n          catchError((error) => {\r\n            if (error.status === 401 || error.status === 403) {\r\n              this.logout();\r\n            }\r\n            return throwError(() => error);\r\n          })\r\n        );\r\n      }\r\n    } else if (platform === 'winpluspharma') {\r\n      // WinPlus platform: needs all three tokens\r\n      if (tokenUser && tokenTenant && tokenLocal) {\r\n        if (!this.apiService.isLoggedIn()) {\r\n          this.logout();\r\n          return throwError(() => new Error('Token has expired'));\r\n        }\r\n\r\n        const cloned = req.clone({\r\n          headers: req.headers\r\n            .set('Authorization', `Bearer ${tokenLocal}`)\r\n            .set('AuthorizationTenant', `BearerTenant ${JSON.parse(tokenTenant).accessToken}`)\r\n            .set('AuthorizationUser', `BearerUser ${JSON.parse(tokenUser).accessToken}`)\r\n        });\r\n        return next.handle(cloned).pipe(\r\n          catchError((error) => {\r\n            if (error.status === 401 || error.status === 403) {\r\n              this.logout();\r\n            }\r\n            return throwError(() => error);\r\n          })\r\n        );\r\n      }\r\n    }\r\n\r\n    return next.handle(req);\r\n  }\r\n\r\n  isTokenExpired(token: string): boolean {\r\n    const decodedToken: any = jwtDecode(token);\r\n    const expirationDate = new Date(decodedToken.exp * 1000);\r\n    return expirationDate < new Date();\r\n  }\r\n\r\n\r\n\r\n  logout() {\r\n    localStorage.removeItem('tokenUser');\r\n    localStorage.removeItem('tokenTenant');\r\n    localStorage.removeItem('token');\r\n    localStorage.removeItem('credentials');\r\n    localStorage.removeItem('ocrMode');\r\n    // Keep src_app to remember platform choice\r\n    // Redirect the user to the login page or show a logout message\r\n  }\r\n}"], "mappings": ";AAEA,SAAqBA,UAAU,QAAQ,MAAM;AAC7C,SAASC,SAAS,QAAQ,YAAY;AACtC,SAASC,UAAU,QAAQ,gBAAgB;;;AAY3C,OAAM,MAAOC,eAAe;EAE1BC,YAAoBC,UAAsB;IAAtB,KAAAA,UAAU,GAAVA,UAAU;EAAgB;EAE9CC,SAASA,CAACC,GAAqB,EAAEC,IAAiB;IAChD,MAAMC,SAAS,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;IACnD,MAAMC,WAAW,GAAGF,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;IACvD,MAAME,UAAU,GAAGH,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAChD,MAAMG,QAAQ,GAAGJ,YAAY,CAACC,OAAO,CAAC,SAAS,CAAC;IAEhD,IAAIG,QAAQ,KAAK,YAAY,EAAE;MAC7B;MACA,IAAIL,SAAS,IAAII,UAAU,EAAE;QAC3B,IAAI,CAAC,IAAI,CAACR,UAAU,CAACU,UAAU,EAAE,EAAE;UACjC,IAAI,CAACC,MAAM,EAAE;UACb,OAAOhB,UAAU,CAAC,MAAM,IAAIiB,KAAK,CAAC,mBAAmB,CAAC,CAAC;;QAGzD,MAAMC,MAAM,GAAGX,GAAG,CAACY,KAAK,CAAC;UACvBC,OAAO,EAAEb,GAAG,CAACa,OAAO,CACjBC,GAAG,CAAC,eAAe,EAAE,UAAUR,UAAU,EAAE,CAAC,CAC5CQ,GAAG,CAAC,mBAAmB,EAAE,cAAcC,IAAI,CAACC,KAAK,CAACd,SAAS,CAAC,CAACe,WAAW,EAAE;SAC9E,CAAC;QACF,OAAOhB,IAAI,CAACiB,MAAM,CAACP,MAAM,CAAC,CAACQ,IAAI,CAC7BxB,UAAU,CAAEyB,KAAK,IAAI;UACnB,IAAIA,KAAK,CAACC,MAAM,KAAK,GAAG,IAAID,KAAK,CAACC,MAAM,KAAK,GAAG,EAAE;YAChD,IAAI,CAACZ,MAAM,EAAE;;UAEf,OAAOhB,UAAU,CAAC,MAAM2B,KAAK,CAAC;QAChC,CAAC,CAAC,CACH;;KAEJ,MAAM,IAAIb,QAAQ,KAAK,eAAe,EAAE;MACvC;MACA,IAAIL,SAAS,IAAIG,WAAW,IAAIC,UAAU,EAAE;QAC1C,IAAI,CAAC,IAAI,CAACR,UAAU,CAACU,UAAU,EAAE,EAAE;UACjC,IAAI,CAACC,MAAM,EAAE;UACb,OAAOhB,UAAU,CAAC,MAAM,IAAIiB,KAAK,CAAC,mBAAmB,CAAC,CAAC;;QAGzD,MAAMC,MAAM,GAAGX,GAAG,CAACY,KAAK,CAAC;UACvBC,OAAO,EAAEb,GAAG,CAACa,OAAO,CACjBC,GAAG,CAAC,eAAe,EAAE,UAAUR,UAAU,EAAE,CAAC,CAC5CQ,GAAG,CAAC,qBAAqB,EAAE,gBAAgBC,IAAI,CAACC,KAAK,CAACX,WAAW,CAAC,CAACY,WAAW,EAAE,CAAC,CACjFH,GAAG,CAAC,mBAAmB,EAAE,cAAcC,IAAI,CAACC,KAAK,CAACd,SAAS,CAAC,CAACe,WAAW,EAAE;SAC9E,CAAC;QACF,OAAOhB,IAAI,CAACiB,MAAM,CAACP,MAAM,CAAC,CAACQ,IAAI,CAC7BxB,UAAU,CAAEyB,KAAK,IAAI;UACnB,IAAIA,KAAK,CAACC,MAAM,KAAK,GAAG,IAAID,KAAK,CAACC,MAAM,KAAK,GAAG,EAAE;YAChD,IAAI,CAACZ,MAAM,EAAE;;UAEf,OAAOhB,UAAU,CAAC,MAAM2B,KAAK,CAAC;QAChC,CAAC,CAAC,CACH;;;IAIL,OAAOnB,IAAI,CAACiB,MAAM,CAAClB,GAAG,CAAC;EACzB;EAEAsB,cAAcA,CAACC,KAAa;IAC1B,MAAMC,YAAY,GAAQ9B,SAAS,CAAC6B,KAAK,CAAC;IAC1C,MAAME,cAAc,GAAG,IAAIC,IAAI,CAACF,YAAY,CAACG,GAAG,GAAG,IAAI,CAAC;IACxD,OAAOF,cAAc,GAAG,IAAIC,IAAI,EAAE;EACpC;EAIAjB,MAAMA,CAAA;IACJN,YAAY,CAACyB,UAAU,CAAC,WAAW,CAAC;IACpCzB,YAAY,CAACyB,UAAU,CAAC,aAAa,CAAC;IACtCzB,YAAY,CAACyB,UAAU,CAAC,OAAO,CAAC;IAChCzB,YAAY,CAACyB,UAAU,CAAC,aAAa,CAAC;IACtCzB,YAAY,CAACyB,UAAU,CAAC,SAAS,CAAC;IAClC;IACA;EACF;;mBA5EWhC,eAAe;;mBAAfA,gBAAe,EAAAiC,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;AAAA;;SAAfpC,gBAAe;EAAAqC,OAAA,EAAfrC,gBAAe,CAAAsC;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}