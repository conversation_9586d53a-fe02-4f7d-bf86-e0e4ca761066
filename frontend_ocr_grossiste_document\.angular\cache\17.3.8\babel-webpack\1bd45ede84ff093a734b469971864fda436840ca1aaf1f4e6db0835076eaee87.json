{"ast": null, "code": "// This file can be replaced during build by using the `fileReplacements` array.\n// `ng build` replaces `environment.ts` with `environment.prod.ts`.\n// The list of file replacements can be found in `angular.json`.\nexport const environment = {\n  production: true,\n  platform: 'mobile',\n  // webSocketUrl: 'ws://192.168.101.176:8085/ws',\n  // apiUrl: 'http://192.168.101.176:8085',  // Adjust to your FastAPI local URL\n  // webSockeRealTimetUrl: 'ws://192.168.101.176:8085/websocket/realtime_processing',\n  // webSocketUrl: 'ws://192.168.101.176:8088/ws',\n  // apiUrl: 'http://192.168.101.176:8088',  // Adjust to your FastAPI local URL\n  // webSockeRealTimetUrl: 'ws://192.168.101.176:8088/websocket/realtime_processing'\n  webSocketUrl: 'wss://winproduit.sophatel.com:8001/ws',\n  apiUrl: 'https://winproduit.sophatel.com:8001',\n  webSockeRealTimetUrl: 'wss://winproduit.sophatel.com:8001/websocket/realtime_processing'\n  // webSocketUrl: 'wss://windoc-api.sophatel.com/ws',\n  // apiUrl: 'https://windoc-api.sophatel.com',  // Adjust to your FastAPI local URL\n  // webSockeRealTimetUrl: 'wss://windoc-api.sophatel.com/websocket/realtime_processing'\n  // webSocketUrl: 'wss://winproduit.sophatel.com:8000/ws',\n  // apiUrl: 'https://winproduit.sophatel.com:8000',  // Adjust to your FastAPI local URL\n  // webSockeRealTimetUrl: 'wss://winproduit.sophatel.com:8000/websocket/realtime_processing'\n  // webSocketUrl: 'wss://ocr-api.abdohero.com/ws', // Secure WebSocket URL\n  // apiUrl: 'https://ocr-api.abdohero.com',  // Adjust to your FastAPI server URL\n  // webSockeRealTimetUrl: 'wss://ocr-api.abdohero.com/websocket/realtime_processing'\n};\n/*\n * For easier debugging in development mode, you can import the following file\n * to ignore zone related error stack frames such as `zone.run`, `zoneDelegate.invokeTask`.\n *\n * This import should be commented out in production mode because it will have a negative impact\n * on performance if an error is thrown.\n */\n// import 'zone.js/plugins/zone-error';  // Included with Angular CLI.", "map": {"version": 3, "names": ["environment", "production", "platform", "webSocketUrl", "apiUrl", "webSockeRealTimetUrl"], "sources": ["C:\\Users\\<USER>\\Downloads\\Work __<PERSON><PERSON><PERSON><PERSON><PERSON>_ouhna\\OCR_DOCUMENT_GROSSISTE\\Frontend ocr grossiste document\\frontend_ocr_grossiste_document\\src\\environments\\environment.ts"], "sourcesContent": ["// This file can be replaced during build by using the `fileReplacements` array.\r\n// `ng build` replaces `environment.ts` with `environment.prod.ts`.\r\n// The list of file replacements can be found in `angular.json`.\r\n\r\nexport const environment = {\r\n  production: true,\r\n  platform: 'mobile', // 'web' or 'mobile'\r\n\r\n\r\n  // webSocketUrl: 'ws://192.168.101.176:8085/ws',\r\n  // apiUrl: 'http://192.168.101.176:8085',  // Adjust to your FastAPI local URL\r\n  // webSockeRealTimetUrl: 'ws://192.168.101.176:8085/websocket/realtime_processing',\r\n\r\n  // webSocketUrl: 'ws://192.168.101.176:8088/ws',\r\n  // apiUrl: 'http://192.168.101.176:8088',  // Adjust to your FastAPI local URL\r\n  // webSockeRealTimetUrl: 'ws://192.168.101.176:8088/websocket/realtime_processing'\r\n\r\n  webSocketUrl: 'wss://winproduit.sophatel.com:8001/ws',\r\n  apiUrl: 'https://winproduit.sophatel.com:8001',  // Adjust to your FastAPI local URL\r\n  webSockeRealTimetUrl: 'wss://winproduit.sophatel.com:8001/websocket/realtime_processing'\r\n\r\n  // webSocketUrl: 'wss://windoc-api.sophatel.com/ws',\r\n  // apiUrl: 'https://windoc-api.sophatel.com',  // Adjust to your FastAPI local URL\r\n  // webSockeRealTimetUrl: 'wss://windoc-api.sophatel.com/websocket/realtime_processing'\r\n\r\n  // webSocketUrl: 'wss://winproduit.sophatel.com:8000/ws',\r\n  // apiUrl: 'https://winproduit.sophatel.com:8000',  // Adjust to your FastAPI local URL\r\n  // webSockeRealTimetUrl: 'wss://winproduit.sophatel.com:8000/websocket/realtime_processing'\r\n\r\n\r\n  // webSocketUrl: 'wss://ocr-api.abdohero.com/ws', // Secure WebSocket URL\r\n  // apiUrl: 'https://ocr-api.abdohero.com',  // Adjust to your FastAPI server URL\r\n  // webSockeRealTimetUrl: 'wss://ocr-api.abdohero.com/websocket/realtime_processing'\r\n};\r\n\r\n/*\r\n * For easier debugging in development mode, you can import the following file\r\n * to ignore zone related error stack frames such as `zone.run`, `zoneDelegate.invokeTask`.\r\n *\r\n * This import should be commented out in production mode because it will have a negative impact\r\n * on performance if an error is thrown.\r\n */\r\n// import 'zone.js/plugins/zone-error';  // Included with Angular CLI."], "mappings": "AAAA;AACA;AACA;AAEA,OAAO,MAAMA,WAAW,GAAG;EACzBC,UAAU,EAAE,IAAI;EAChBC,QAAQ,EAAE,QAAQ;EAGlB;EACA;EACA;EAEA;EACA;EACA;EAEAC,YAAY,EAAE,uCAAuC;EACrDC,MAAM,EAAE,sCAAsC;EAC9CC,oBAAoB,EAAE;EAEtB;EACA;EACA;EAEA;EACA;EACA;EAGA;EACA;EACA;CACD;AAED;;;;;;;AAOA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}