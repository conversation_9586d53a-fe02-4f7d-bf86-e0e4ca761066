import json
import re
from datetime import datetime


def merge_parts(general, header, table, footer):
    return {
        "general": general.to_dict(),
        "header": header.to_dict(),
        "table": [entry.to_dict() for entry in table],
        "footer": footer.to_dict()

    }


class DocumentModel:
    class General:
        def __init__(self, random_id=None, model_name=None, user_id=None, date_export=None, images_url_path=None, src_app='winpluspharma'):
            self.random_id = random_id
            self.model_name = model_name
            self.user_id = user_id
            self.date_export = date_export  # typically filled with the current date/time during processing
            self.images_url_path = images_url_path
            self.src_app = src_app  # Platform source identifier


        def to_dict(self):
            return {
                "random_id": self.random_id,
                "model_name": self.model_name,
                "user_id": self.user_id,
                "date_export": self.date_export.strftime('%Y-%m-%d %H:%M:%S') if self.date_export else None,
                "images_url_path": self.images_url_path,
                "src_app": self.src_app,
            }

    class Header:
        def __init__(self):
            self.name_fournisseur = None
            self.ice_fournisseur = None
            self.contact_info_fournisseur = None
            self.patente_fournisseur = None
            self.cnss_fournisseur = None
            self.adresse_fournisseur = None
            # self.name_client = None
            self.ice_client = None
            self.info_client = None
            self.patente_client = None
            self.num_bl = None
            self.num_page = None
            self.date_bl = None
            self.no = None
            self.num_zone = None
            self.num_client = None

        def to_dict(self):
            return {
                "name_fournisseur": self.name_fournisseur,
                "ice_fournisseur": self.ice_fournisseur,
                "contact_info_fournisseur": self.contact_info_fournisseur,
                "patente_fournisseur": self.patente_fournisseur,
                "cnss_fournisseur": self.cnss_fournisseur,
                "adresse_fournisseur": self.adresse_fournisseur,
#                 "name_client": self.name_client,
                "ice_client": self.ice_client,
                "info_client": self.info_client,
                "patente_client": self.patente_client,
                "num_bl": self.num_bl,
                "num_page": self.num_page,
                "date_bl": self.date_bl,
                "no": self.no,
                "num_zone": self.num_zone,
                "num_client": self.num_client
            }

    class Table:
        _id_counter = 0

        def __init__(self):
            self.id = self._generate_id()
            self.quantity = None
            self.forme_galenique = None
            self.designation = None
            self.pph = None
            self.ppv = None
            self.total_ttc = None
            self.tva = None
            self.date_per = None
            self.num_lot = None
            self.qty_duplicate = None
            self.qty_ordered = None
            self.code_produit = None
            self.gf = None
            self.pu_client = None
            self.additional_info = None

        @classmethod
        def _generate_id(cls):
            cls._id_counter += 1
            return cls._id_counter

        def to_dict(self):
            return {
                "id": self.id,
                "quantity": self.quantity,
                "qty_ordered": self.qty_ordered,
                "qty_duplicate": self.qty_duplicate,
                "designation": self.designation,
                "forme_galenique": self.forme_galenique,
                "pph": self.pph,
                "ppv": self.ppv,
                "total_ttc": self.total_ttc,
                "tva": self.tva,
                "date_per": self.date_per,
                "num_lot": self.num_lot,
                "code_produit": self.code_produit,
                "gf": self.gf,
                "pu_client": self.pu_client,
                "additional_info": self.additional_info
            }

    class Footer:
        def __init__(self):
            self.total_ttc_global = None
            self.total_lignes = None
            self.total_qty_livree = None
            self.total_ppv = None
            self.total_pph = None
            self.total_ttc = None
            self.total_pro = None
            self.total_produit_net_ttc = None
            self.cumul_periode = None
            self.avoir_regular = None

        def to_dict(self):
            return {
                "total_ttc_global": self.total_ttc_global,
                "total_lignes": self.total_lignes,
                "total_qty_livree": self.total_qty_livree,
                "total_ppv": self.total_ppv,
                "total_pph": self.total_pph,
                "total_ttc": self.total_ttc,
                "total_pro": self.total_pro,
                "total_produit_net_ttc": self.total_produit_net_ttc,
                "cumul_periode": self.cumul_periode,
                "avoir_regular": self.avoir_regular
            }

    def __init__(self):
        self.general = self.General()  # Initialize the general section
        self.header = self.Header()
        self.table = []  # This can be a list to handle multiple table rows
        self.footer = self.Footer()

    def to_dict(self):
        return {
            "general": self.general.to_dict(),
            "header": self.header.to_dict(),
            "table": [entry.to_dict() for entry in self.table],
            "footer": self.footer.to_dict()
        }

    def to_json(self):
        return json.dumps(self.to_dict(), ensure_ascii=False, indent=4)

    def remove_special_characters(self):
        def clean_text(text):
            if text is not None:
                if not isinstance(text, str):
                    text = str(text)
                return re.sub(r"[^a-zA-Z0-9/%().,:-]+", " ", text)
            return text

        # Clean header fields
        for attr, value in vars(self.header).items():
            setattr(self.header, attr, clean_text(value))

        # Clean table entries
        for entry in self.table:
            for attr, value in vars(entry).items():
                setattr(entry, attr, clean_text(value))

        # Clean footer fields
        for attr, value in vars(self.footer).items():
            setattr(self.footer, attr, clean_text(value))

    def set_general_info(self, random_id, model_name, user_id, date_export=None, images_url_path=None):
        if not date_export:
            date_export = datetime.now()
        self.general = self.General(
            random_id,
            model_name,
            user_id,
            date_export,
            images_url_path
        )
