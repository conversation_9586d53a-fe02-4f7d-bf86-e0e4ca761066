from fastapi import APIRouter, HTTPException, WebSocketDisconnect, Depends, Header, Body
from src.app.dependencies import get_current_user, check_headers_dynamic, check_tenant_user_headers_winplus
import traceback
import asyncio
import logging
import json
from ..config import API_URL, WINPLUS_URL
from ..services.websocket_manager import manager
from ..utils.document_model import DocumentModel
from ..utils.helpers import get_temp_path, write_image_async, get_base_name, get_supplier_id
from ..utils.models import List, ImageData, PreBlOcrResponse, UpdateStatusRequest, ProcessOcrMultiRequest
from ..utils.db_operations import save_response_to_db
import uuid
from datetime import datetime
from src.app.app import process_model_image_with_module, advanced_mindee_process_ocr
from src.app.services import identify_supplier
from concurrent.futures import ThreadPoolExecutor

import httpx
from jwt.exceptions import PyJWTError
import jwt
import sqlite3

router = APIRouter()

logger = logging.getLogger(__name__)

# Define the URLs
apiUrl = API_URL

# Initialize a ThreadPoolExecutor
executor = ThreadPoolExecutor(max_workers=10)

@router.post("/", dependencies=[Depends(get_current_user), Depends(check_headers_dynamic)])
async def process_ocr_multi_endpoint(
    request: ProcessOcrMultiRequest,
    auth_data: dict = Depends(check_headers_dynamic)
):
    try:

        # Log the entire request in one line
        logging.info(f"Received request from process_ocr_multi_endpoint: {request.dict()}")

        # Run `process_identify_supplier` in a ThreadPoolExecutor
        loop = asyncio.get_event_loop()
        result = await loop.run_in_executor(
            executor,
            lambda: asyncio.run(process_ocr_multi_process(request, auth_data))
        )

        # Return the result
        return result

    except HTTPException as http_exc:
        logging.error(f"Erreur HTTP: {http_exc.detail}")
        raise http_exc
    except Exception as e:
        logging.error(f"Erreur inattendue: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail="Erreur interne du serveur")


async def process_ocr_multi_process(
    request: ProcessOcrMultiRequest,
    auth_data: dict = None
):
    images = request.images
    ocr_mode = request.ocr_mode.upper()
    src_app = request.src_app  # Extract src_app from request

    responses = []
    total_images = len(images)
    increment_per_image = 100 / total_images
    current_progress = 0

    random_id = images[0].random_id

    original_image_dir_path = get_temp_path() / "origin_images_output"
    cropped_image_dir_path = get_temp_path() / "smart_crop_output"
    filtered_image_dir_path = get_temp_path() / "magic_pro_filter_output"
    output_dir_path = get_temp_path() / "output_preprocessing"
    output_dir_path.mkdir(exist_ok=True, parents=True)

    """ Extract data from the Authorization (Token, UserID, TenantID, ...)"""
    try:
        # Extract the token strings from the auth_data dictionary
        authorizationuser = auth_data.get("AuthorizationUser", "")
        authorizationtenant = auth_data.get("AuthorizationTenant", "")

        user_token = authorizationuser.split(" ", 1)[1] if " " in authorizationuser else authorizationuser
        tenant_token = authorizationtenant.split(" ", 1)[1] if " " in authorizationtenant else authorizationtenant

        logging.info(f"Extracted user_token: {user_token}")
        logging.info(f"Extracted tenant_token: {tenant_token}")
        logging.info(f"Platform: {auth_data.get('platform', 'unknown')}")

        # Decode the JWT tokens based on platform
        platform = auth_data.get('platform', 'winpluspharma')
        user_token_data = jwt.decode(user_token, options={"verify_signature": False})

        if platform == 'winpluspharma' and tenant_token:
            # WinPlus platform - decode tenant token
            tenant_token_data = jwt.decode(tenant_token, options={"verify_signature": False})
            user_id = user_token_data.get("sub")
            tenant_code = user_token_data.get("tenant_code")
            tenant_id = tenant_token_data.get("sub")

            logging.info(f"WinPlus - Extracted user_token_data: {user_token_data}")
            logging.info(f"WinPlus - Extracted tenant_token_data: {tenant_token_data}")

            if not all([user_id, tenant_code, tenant_id]):
                raise ValueError("Missing required WinPlus authorization data")

        else:
            # Pharmalien platform - no tenant token
            user_id = user_token_data.get("sub")
            tenant_code = user_token_data.get("tenant_code", "pharmalien_default")
            tenant_id = user_token_data.get("tenant_id", "pharmalien_tenant")

            logging.info(f"Pharmalien - Extracted user_token_data: {user_token_data}")

            if not user_id:
                raise ValueError("Missing required Pharmalien authorization data")

        logging.info(f"Extracted user_id: {user_id}")
        logging.info(f"Extracted tenant_code: {tenant_code}")
        logging.info(f"Extracted tenant_id: {tenant_id}")

    except (PyJWTError, ValueError) as e:
        logging.error(f"Error parsing authorization headers: {str(e)}")
        raise HTTPException(status_code=401, detail=f"Invalid authorization data: {str(e)}")
    """ Extract data from the Authorization (Token, UserID, TenantID, ...)"""

    for image_data in images:
        try:
            # Generate a UUID if not provided
            random_id = image_data.random_id or str(uuid.uuid4())[
                                                :8] if image_data.random_id is None else image_data.random_id  # Extract the first 8 characters

            # image_path = r'C:\Users\<USER>\Downloads\Work __Abderrahmane_ouhna\OCR_DOCUMENT_GROSSISTE\ocr_document_grossiste\temp\origin_BL\BLS_1.jpg'

            # Save the uploaded image to the temporary directory
            image_path_processed = get_temp_path() / image_data.image  # Path to save the processed image (cropped, filtered, etc.)

            current_date = datetime.now().strftime("%Y%m%d%H%M%S")
            base_name_origin = f'origin_{current_date}_{get_base_name(image_path_processed)}'

            image_path_to_preprocessing = output_dir_path / base_name_origin  # Path to save the image before preprocessing (extract data OCR)
            await write_image_async(image_path_to_preprocessing, image_path_processed)

            # get the image from a directory that have the value random_id in his name
            # Find original, cropped, and filtered images
            original_image_path = next(original_image_dir_path.glob(f"*{random_id}*"), None)
            cropped_image_path = next(cropped_image_dir_path.glob(f"*cropped_{random_id}*"), None)
            filtered_image_path = next(filtered_image_dir_path.glob(f"*{random_id}*"), None)

            if not all([original_image_path, cropped_image_path, filtered_image_path]):
                raise FileNotFoundError("One or more required images not found")

            # Construct image URLs
            images_url_path = {
                "origin": f"{apiUrl}/static/output_preprocessing/{base_name_origin}",
                "cropped": f"{apiUrl}/static/output_preprocessing/{cropped_image_path.name}" if cropped_image_path else None,
                "filtered": f"{apiUrl}/static/output_preprocessing/{filtered_image_path.name}" if filtered_image_path else None
            }

            # Notify the client about progress (Images saved)
            current_progress += increment_per_image * 0.1  # 10% of the work
            try:
                await manager.send_progress(image_data.job_id, current_progress)
                await asyncio.sleep(0.01)
            except (WebSocketDisconnect, RuntimeError) as e:
                logging.error(f"Error sending progress update: {e}")

            # Apply the preprocessing and OCR processing
            try:

                if ocr_mode == "MINDEE_ADVANCED" or request.images[0].model_name.upper() == "GLOBAL" or request.images[0].model_name.upper() == "AUTRE":

                    """ --------------********-- Identify Supplier ----********------------------ """

                    # # Identify Supplier
                    # try:
                    #     document_header = identify_supplier.identify_supplier(str(cropped_image_path), "GLOBAL",
                    #                                                           get_temp_path() / "identify_suppliers",
                    #                                                           random_id)
                    #     supplier_name = document_header.header.name_fournisseur
                    #     logging.info("Identified supplier name: %s", supplier_name)
                    # except Exception as e:
                    #     logging.error(f"Exception during supplier identification: {traceback.format_exc()}")
                    #     raise HTTPException(status_code=500, detail="Erreur lors de l'identification du fournisseur.")

                    # Use Mindee OCR for cropped image
                    document_model, current_progress = await advanced_mindee_process_ocr(
                        image_path=str(cropped_image_path),
                        model= image_data.model_name.upper(),                                                                          # supplier_name,
                        isAPI=True,
                        user_id=user_id,
                        images_url_path=images_url_path,
                        manager=manager,
                        current_progress=current_progress,
                        increment_per_image=increment_per_image * 0.8,

                    )

                    response = {
                        "success": True,
                        "data": document_model.to_dict(),
                        "message": "Processing successful."
                    }
                else:

                    logging.info(f"Processing image: {str(image_path_processed)}")
                    document_model, current_progress = await process_model_image_with_module(
                        image_data.model_name.upper(), str(image_path_processed), output_dir_path, 'default', False, True,
                        images_url_path, manager, current_progress, increment_per_image * 0.8, image_data.job_id, user_id
                    )

                    response = {
                        "success": True,
                        "data": document_model.to_dict(),
                        "message": "Processing successful."
                    }
            except Exception as e:
                logging.error(f"Exception during OCR processing: {traceback.format_exc()}")
                raise HTTPException(status_code=500, detail="Erreur lors du traitement OCR.")

            # Notify the client about progress (Processing complete for current image)
            current_progress += increment_per_image * 0.1  # Final 10% of the work
            try:
                await manager.send_progress(image_data.job_id, current_progress)
                await asyncio.sleep(0.01)
            except (WebSocketDisconnect, RuntimeError) as e:
                logging.error(f"Error sending progress update: {e}")

            responses.append(response)

        except FileNotFoundError as fnf_error:
            logging.error("FileNotFoundError: %s", fnf_error)
            response = {
                "success": False,
                "data": None,
                "message": str(fnf_error)
            }
            responses.append(response)
        except Exception as e:
            logging.error("Exception: %s", e)
            logging.error(traceback.format_exc())
            response = {
                "success": False,
                "data": None,
                "message": "Erreur de traitement de la requête."
            }
            responses.append(response)

    """ Save the response to DB """
    try:
        id_bl = save_response_to_db(responses, user_id, tenant_code, tenant_id, random_id, src_app)
    except sqlite3.Error as e:
        logging.error(f"Database error in process_ocr_multi_endpoint: {e}")
        logging.error(f"Responses: {responses}")
        logging.error(f"User ID: {user_id}")
        logging.error(f"Tenant Code: {tenant_code}")
        logging.error(f"Tenant ID: {tenant_id}")
        raise HTTPException(status_code=500, detail=f"Error saving to database: {str(e)}")
    """ Save the response to DB"""


    return {"success": True, "responses": responses, "ID_BL": id_bl}
