{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Downloads/Work __Abderrahmane_ouhna/OCR_DOCUMENT_GROSSISTE/Frontend ocr grossiste document/frontend_ocr_grossiste_document/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nvar _ApiService;\nimport { HttpHeaders } from '@angular/common/http';\nimport { throwError } from 'rxjs';\nimport { environment } from '../../environments/environment';\nimport { catchError, tap } from 'rxjs/operators';\nimport Swal from 'sweetalert2';\nimport { jwtDecode } from 'jwt-decode';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@ionic/angular\";\nvar OcrMode;\n(function (OcrMode) {\n  OcrMode[\"STANDARD\"] = \"STANDARD\";\n  OcrMode[\"MINDEE_ADVANCED\"] = \"MINDEE_ADVANCED\";\n})(OcrMode || (OcrMode = {}));\nexport class ApiService {\n  constructor(http, alertController) {\n    this.http = http;\n    this.alertController = alertController;\n    this.baseUrl = environment.apiUrl; // Ensure your environment file has the correct base URL\n    // Set common HTTP options\n    this.httpOptions = {\n      headers: new HttpHeaders({\n        'Content-Type': 'application/json'\n      })\n    };\n  }\n  // Generate a unique job ID\n  generateJobId() {\n    return 'job_' + Math.random().toString(36).substr(2, 9);\n  }\n  logRequest(url, method, body) {\n    console.log(`Request: ${method} ${url}`, body);\n  }\n  logResponse(response) {\n    console.log('Response:', response);\n  }\n  handleError(error) {\n    console.error('API Error:', error);\n    return throwError(error);\n  }\n  // Fetch root endpoint\n  getRoot() {\n    return this.http.get(`${this.baseUrl}/`);\n  }\n  // Tenant Login\n  tenantLogin(request) {\n    const url = `${this.baseUrl}/tenant_login`;\n    this.logRequest(url, 'POST', request);\n    return this.http.post(url, request, this.httpOptions).pipe(tap(this.logResponse), catchError(this.handleError));\n  }\n  // User Login\n  userLogin(request, tenantToken) {\n    const url = `${this.baseUrl}/login`;\n    const headers = new HttpHeaders({\n      'Content-Type': 'application/json',\n      'AuthorizationTenant': `BearerTenant ${tenantToken}`\n    });\n    const bodyWithToken = {\n      ...request,\n      tenant_token: tenantToken\n    };\n    this.logRequest(url, 'POST', bodyWithToken);\n    return this.http.post(url, bodyWithToken, {\n      headers\n    }).pipe(tap(this.logResponse), catchError(this.handleError));\n  }\n  // Pharmalien Login\n  pharmalienLogin(request) {\n    const url = `${this.baseUrl}/pharmalien_login`;\n    this.logRequest(url, 'POST', request);\n    return this.http.post(url, request, this.httpOptions).pipe(tap(this.logResponse), catchError(this.handleError));\n  }\n  // Logout function\n  logout() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      return new Promise( /*#__PURE__*/function () {\n        var _ref = _asyncToGenerator(function* (resolve) {\n          const alert = yield _this.alertController.create({\n            header: 'Déconnexion',\n            message: `Confirmer la déconnexion ?`,\n            buttons: [{\n              text: 'Annuler',\n              role: 'cancel',\n              cssClass: 'custom-alert-button cancel',\n              handler: () => {\n                console.log('Confirm Cancel');\n                resolve(); // Resolve even if canceled\n              }\n            }, {\n              text: 'Oui',\n              cssClass: 'custom-alert-button danger',\n              handler: () => {\n                localStorage.removeItem('tokenUser');\n                localStorage.removeItem('tokenTenant');\n                localStorage.removeItem('token');\n                localStorage.removeItem('ocrMode');\n                localStorage.removeItem('forceSupplierGlobal');\n                localStorage.removeItem('selectedSupplier');\n                resolve(); // Resolve after logout\n              }\n            }]\n          });\n          yield alert.present();\n        });\n        return function (_x) {\n          return _ref.apply(this, arguments);\n        };\n      }());\n    })();\n  }\n  // Smart Crop API\n  smartCrop(image, jobId, isScanner) {\n    console.log('API Base URL:', this.baseUrl);\n    const formData = new FormData();\n    formData.append('image', image);\n    formData.append('job_id', jobId); // Include the job ID\n    formData.append('isScanner', isScanner.toString()); // Include the isScanner flag\n    const url = `${this.baseUrl}/smart_crop/`;\n    this.logRequest(url, 'POST', formData);\n    return this.http.post(url, formData).pipe(tap(this.logResponse), catchError(this.handleError));\n  }\n  // Magic Pro Filter API\n  magicProFilter(image, modelName, randomId, jobId) {\n    const formData = new FormData();\n    formData.append('image', image);\n    formData.append('model_name', modelName);\n    formData.append('job_id', jobId !== null && jobId !== void 0 ? jobId : \"\"); // Include the job ID\n    if (randomId) {\n      formData.append('random_id', randomId);\n    }\n    return this.http.post(`${this.baseUrl}/magic_pro_filter/`, formData);\n  }\n  // Identify Supplier API\n  identifySupplier(image, modelName, randomId, jobId) {\n    const formData = new FormData();\n    formData.append('image', image);\n    formData.append('model_name', modelName);\n    formData.append('job_id', jobId !== null && jobId !== void 0 ? jobId : \"\"); // Include the job ID\n    if (randomId) {\n      formData.append('random_id', randomId);\n    }\n    return this.http.post(`${this.baseUrl}/identify_supplier/`, formData);\n  }\n  // Process Image Supp API\n  processImageSupp(request, jobId) {\n    // const jobId = this.generateJobId();  // Get the job ID\n    const modifiedRequest = {\n      ...request,\n      job_id: jobId\n    }; // Include the job ID in the request\n    return this.http.post(`${this.baseUrl}/process_image_supp/`, modifiedRequest, this.httpOptions);\n  }\n  // Process OCR Multi API\n  processOcrMulti(images, jobId, random_id, ocrMode = OcrMode.STANDARD) {\n    const modifiedImages = images.map(image => ({\n      ...image,\n      job_id: jobId\n    }));\n    // Create the request body with images and ocrMode\n    const requestBody = {\n      images: modifiedImages,\n      ocr_mode: ocrMode.toLowerCase() // Convert to lowercase to match backend expectation\n    };\n    return this.http.post(`${this.baseUrl}/process_ocr_multi/`, requestBody, this.httpOptions);\n  }\n  // Update BL Status API to EN_COURS\n  updateBLStatus(blId, status, id_BL_origine, date_BL_origine, supplier_name) {\n    return this.http.put(`${this.baseUrl}/winplus/updateStatus/${blId}`, {\n      status,\n      id_BL_origine,\n      date_BL_origine,\n      supplier_name\n    });\n  }\n  // Get All Suppliers list\n  getAllSuppliers() {\n    return this.http.get(`${this.baseUrl}/suppliers`);\n  }\n  showErrorAlert(message) {\n    const messageDisplayed = message !== \"\" ? message : \"Il y a eu une erreur de compréhension de la requête. Veuillez réessayer plus tard.\";\n    Swal.fire({\n      icon: 'error',\n      title: 'Format de l\\'image incorrecte !',\n      html: messageDisplayed,\n      footer: '<a href=\"/guide\">Comment capturer une image de qualité ?</a>',\n      showConfirmButton: false,\n      showCloseButton: true,\n      customClass: {\n        closeButton: 'custom-close-button',\n        popup: 'custom-popup',\n        footer: 'custom-footer' // Custom class for the footer\n      }\n    });\n    // localStorage.removeItem('ocrMode');\n    // localStorage.removeItem('forceSupplierGlobal');\n    localStorage.removeItem('selectedSupplier');\n  }\n  isLoggedIn() {\n    const token = localStorage.getItem('token');\n    const tokenUser = localStorage.getItem('tokenUser');\n    const tokenTenant = localStorage.getItem('tokenTenant');\n    if (!token || !tokenUser || !tokenTenant) {\n      return false;\n    }\n    try {\n      const decodedToken = this.decodeToken(token);\n      if (!this.isTokenValid(decodedToken)) {\n        return false;\n      }\n    } catch (e) {\n      console.error('Token decoding failed', e);\n      return false;\n    }\n    return true;\n  }\n  decodeToken(token) {\n    return jwtDecode(token);\n  }\n  isTokenValid(decodedToken) {\n    // Check if token has expired\n    const currentTime = Math.floor(Date.now() / 1000);\n    if (decodedToken.exp < currentTime) {\n      return false;\n    }\n    return true;\n  }\n  // ## -- Medicament API -- ##\n  /**\n   * Get medicament information and suggestions from OCR\n   * @param image Image file to analyze\n   * @param jobId Job ID for tracking progress\n   * @returns Promise with medicament suggestions\n   */\n  getMedicamentInfo(image, jobId) {\n    console.log('API Base URL:', this.baseUrl);\n    const formData = new FormData();\n    formData.append('image', image);\n    formData.append('job_id', jobId); // Include the job ID\n    const url = `${this.baseUrl}/medicament_ocr_tap/`;\n    console.log('Making request to:', url);\n    return this.http.post(url, formData);\n  }\n}\n_ApiService = ApiService;\n_ApiService.ɵfac = function ApiService_Factory(t) {\n  return new (t || _ApiService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.AlertController));\n};\n_ApiService.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n  token: _ApiService,\n  factory: _ApiService.ɵfac,\n  providedIn: 'root'\n});", "map": {"version": 3, "names": ["HttpHeaders", "throwError", "environment", "catchError", "tap", "<PERSON><PERSON>", "jwtDecode", "OcrMode", "ApiService", "constructor", "http", "alertController", "baseUrl", "apiUrl", "httpOptions", "headers", "generateJobId", "Math", "random", "toString", "substr", "logRequest", "url", "method", "body", "console", "log", "logResponse", "response", "handleError", "error", "getRoot", "get", "tenantLogin", "request", "post", "pipe", "userLogin", "tenantToken", "bodyWithToken", "tenant_token", "pharmalien<PERSON><PERSON>in", "logout", "_this", "_asyncToGenerator", "Promise", "_ref", "resolve", "alert", "create", "header", "message", "buttons", "text", "role", "cssClass", "handler", "localStorage", "removeItem", "present", "_x", "apply", "arguments", "smartCrop", "image", "jobId", "isScanner", "formData", "FormData", "append", "magicProFilter", "modelName", "randomId", "identifySupplier", "processImageSupp", "modifiedRequest", "job_id", "processOcrMulti", "images", "random_id", "ocrMode", "STANDARD", "modifiedImages", "map", "requestBody", "ocr_mode", "toLowerCase", "updateBLStatus", "blId", "status", "id_BL_origine", "date_BL_origine", "supplier_name", "put", "getAllSuppliers", "showError<PERSON><PERSON>t", "messageDisplayed", "fire", "icon", "title", "html", "footer", "showConfirmButton", "showCloseButton", "customClass", "closeButton", "popup", "isLoggedIn", "token", "getItem", "tokenUser", "tokenTenant", "decodedToken", "decodeToken", "isTokenValid", "e", "currentTime", "floor", "Date", "now", "exp", "getMedicamentInfo", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Downloads\\Work __<PERSON><PERSON><PERSON><PERSON><PERSON>_ouhna\\OCR_DOCUMENT_GROSSISTE\\Frontend ocr grossiste document\\frontend_ocr_grossiste_document\\src\\app\\services\\api.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpClient, HttpHeaders, HttpErrorResponse } from '@angular/common/http';\r\nimport { Observable, throwError  } from 'rxjs';\r\nimport { environment } from '../../environments/environment';\r\nimport { Coordinates } from '../../models/coordinates';\r\nimport { ProcessDocData } from 'src/models/ProcessDocData';\r\nimport { ProcessImageSuppRequest } from 'src/models/ProcessImageSuppRequest';\r\nimport { ImageData } from 'src/models/ImageData';\r\nimport { catchError, tap, switchMap } from 'rxjs/operators';\r\nimport { LoginRequest, LoginResponse, TenantLoginRequest, TenantLoginResponse, PharmalienLoginRequest, PharmalienLoginResponse } from '../../models/login';\r\nimport { AlertController } from '@ionic/angular';\r\nimport Swal from 'sweetalert2';\r\nimport { jwtDecode } from 'jwt-decode';\r\n\r\nenum OcrMode {\r\n  STANDARD = 'STANDARD',\r\n  MINDEE_ADVANCED = 'MINDEE_ADVANCED'\r\n}\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class ApiService {\r\n  private baseUrl = environment.apiUrl;  // Ensure your environment file has the correct base URL\r\n\r\n\r\n  constructor(private http: HttpClient, private alertController: AlertController,) { }\r\n\r\n  // Set common HTTP options\r\n  private httpOptions = {\r\n    headers: new HttpHeaders({\r\n      'Content-Type': 'application/json'\r\n    })\r\n  };\r\n\r\n  // Generate a unique job ID\r\n  generateJobId(): string {\r\n    return 'job_' + Math.random().toString(36).substr(2, 9);\r\n  }\r\n\r\n  private logRequest(url: string, method: string, body?: any) {\r\n    console.log(`Request: ${method} ${url}`, body);\r\n  }\r\n\r\n  private logResponse(response: any) {\r\n    console.log('Response:', response);\r\n  }\r\n\r\n  private handleError(error: HttpErrorResponse) {\r\n    console.error('API Error:', error);\r\n    return throwError(error);\r\n  }\r\n\r\n  // Fetch root endpoint\r\n  getRoot(): Observable<any> {\r\n    return this.http.get(`${this.baseUrl}/`);\r\n  }\r\n\r\n   // Tenant Login\r\n  tenantLogin(request: TenantLoginRequest): Observable<TenantLoginResponse> {\r\n    const url = `${this.baseUrl}/tenant_login`;\r\n    this.logRequest(url, 'POST', request);\r\n    return this.http.post<TenantLoginResponse>(url, request, this.httpOptions).pipe(\r\n      tap(this.logResponse),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // User Login\r\n  userLogin(request: LoginRequest, tenantToken: string): Observable<LoginResponse> {\r\n    const url = `${this.baseUrl}/login`;\r\n    const headers = new HttpHeaders({\r\n      'Content-Type': 'application/json',\r\n      'AuthorizationTenant': `BearerTenant ${tenantToken}`\r\n    });\r\n    const bodyWithToken = { ...request, tenant_token: tenantToken };\r\n    this.logRequest(url, 'POST', bodyWithToken);\r\n    return this.http.post<LoginResponse>(url, bodyWithToken, { headers }).pipe(\r\n      tap(this.logResponse),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // Pharmalien Login\r\n  pharmalienLogin(request: PharmalienLoginRequest): Observable<PharmalienLoginResponse> {\r\n    const url = `${this.baseUrl}/pharmalien_login`;\r\n    this.logRequest(url, 'POST', request);\r\n    return this.http.post<PharmalienLoginResponse>(url, request, this.httpOptions).pipe(\r\n      tap(this.logResponse),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // Logout function\r\n  async logout(): Promise<void> {\r\n    return new Promise(async (resolve) => {\r\n      const alert = await this.alertController.create({\r\n        header: 'Déconnexion',\r\n        message: `Confirmer la déconnexion ?`,\r\n        buttons: [\r\n          {\r\n            text: 'Annuler',\r\n            role: 'cancel',\r\n            cssClass: 'custom-alert-button cancel',\r\n            handler: () => {\r\n              console.log('Confirm Cancel');\r\n              resolve(); // Resolve even if canceled\r\n            },\r\n          },\r\n          {\r\n            text: 'Oui',\r\n            cssClass: 'custom-alert-button danger',\r\n            handler: () => {\r\n              localStorage.removeItem('tokenUser');\r\n              localStorage.removeItem('tokenTenant');\r\n              localStorage.removeItem('token');\r\n              localStorage.removeItem('ocrMode');\r\n              localStorage.removeItem('forceSupplierGlobal');\r\n              localStorage.removeItem('selectedSupplier');\r\n              resolve(); // Resolve after logout\r\n            },\r\n          },\r\n        ],\r\n      });\r\n  \r\n      await alert.present();\r\n    });\r\n  }\r\n\r\n  // Smart Crop API\r\n  smartCrop(image: File, jobId: string, isScanner: boolean): Observable<any> {\r\n    console.log('API Base URL:', this.baseUrl);\r\n\r\n    const formData: FormData = new FormData();\r\n    formData.append('image', image);\r\n    formData.append('job_id', jobId);  // Include the job ID\r\n    formData.append('isScanner', isScanner.toString());  // Include the isScanner flag\r\n    const url = `${this.baseUrl}/smart_crop/`;\r\n    this.logRequest(url, 'POST', formData);\r\n    return this.http.post<any>(url, formData).pipe(\r\n      tap(this.logResponse),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // Magic Pro Filter API\r\n  magicProFilter(image: File, modelName: string, randomId?: string, jobId?: string): Observable<any> {\r\n    const formData: FormData = new FormData();\r\n    formData.append('image', image);\r\n    formData.append('model_name', modelName);\r\n    formData.append('job_id', jobId ?? \"\");  // Include the job ID\r\n    if (randomId) {\r\n      formData.append('random_id', randomId);\r\n    }\r\n\r\n    return this.http.post<any>(`${this.baseUrl}/magic_pro_filter/`, formData);\r\n  }\r\n\r\n  // Identify Supplier API\r\n  identifySupplier(image: File, modelName: string, randomId?: string, jobId?: string): Observable<any> {\r\n    const formData: FormData = new FormData();\r\n    formData.append('image', image);\r\n    formData.append('model_name', modelName);\r\n    formData.append('job_id', jobId ?? \"\");  // Include the job ID\r\n    if (randomId) {\r\n      formData.append('random_id', randomId);\r\n    }\r\n\r\n    return this.http.post<any>(`${this.baseUrl}/identify_supplier/`, formData);\r\n  }\r\n\r\n  // Process Image Supp API\r\n  processImageSupp(request: ProcessImageSuppRequest, jobId: string): Observable<any> {\r\n    // const jobId = this.generateJobId();  // Get the job ID\r\n    const modifiedRequest = { ...request, job_id: jobId };  // Include the job ID in the request\r\n    return this.http.post<any>(`${this.baseUrl}/process_image_supp/`, modifiedRequest, this.httpOptions);\r\n  }\r\n\r\n  // Process OCR Multi API\r\n  processOcrMulti(images: ImageData[], jobId: string, random_id: string, ocrMode: OcrMode = OcrMode.STANDARD): Observable<any> {\r\n    const modifiedImages = images.map(image => ({ ...image, job_id: jobId }));\r\n    \r\n    // Create the request body with images and ocrMode\r\n    const requestBody = {\r\n      images: modifiedImages,\r\n      ocr_mode: ocrMode.toLowerCase() // Convert to lowercase to match backend expectation\r\n    };\r\n  \r\n    return this.http.post(`${this.baseUrl}/process_ocr_multi/`, requestBody, this.httpOptions);\r\n  }\r\n\r\n  // Update BL Status API to EN_COURS\r\n  updateBLStatus(blId: string, status: string, id_BL_origine: string, date_BL_origine: string, supplier_name: string): Observable<any> {\r\n    return this.http.put(`${this.baseUrl}/winplus/updateStatus/${blId}`, { status, id_BL_origine, date_BL_origine, supplier_name });\r\n  }\r\n\r\n  // Get All Suppliers list\r\n  getAllSuppliers(): Observable<any> {\r\n    return this.http.get(`${this.baseUrl}/suppliers`);\r\n  }\r\n\r\n  showErrorAlert(message: string) {\r\n    const messageDisplayed = message !== \"\" ? message : \"Il y a eu une erreur de compréhension de la requête. Veuillez réessayer plus tard.\";\r\n  \r\n    Swal.fire({\r\n      icon: 'error',\r\n      title: 'Format de l\\'image incorrecte !',\r\n      html: messageDisplayed,\r\n      footer: '<a href=\"/guide\">Comment capturer une image de qualité ?</a>',\r\n      showConfirmButton: false,  // Remove the confirm button\r\n      showCloseButton: true,     // Add a close button\r\n      customClass: {\r\n        closeButton: 'custom-close-button',  // Custom class for the close button\r\n        popup: 'custom-popup',               // Custom class for the popup for additional styling if needed\r\n        footer: 'custom-footer'              // Custom class for the footer\r\n      }\r\n    });\r\n\r\n    // localStorage.removeItem('ocrMode');\r\n    // localStorage.removeItem('forceSupplierGlobal');\r\n    localStorage.removeItem('selectedSupplier');\r\n\r\n\r\n  }\r\n\r\n  isLoggedIn(): boolean {\r\n    const token = localStorage.getItem('token');\r\n    const tokenUser = localStorage.getItem('tokenUser');\r\n    const tokenTenant = localStorage.getItem('tokenTenant');\r\n\r\n    if (!token || !tokenUser || !tokenTenant) {\r\n      return false;\r\n    }\r\n\r\n    try {\r\n      const decodedToken = this.decodeToken(token);\r\n      if (!this.isTokenValid(decodedToken)) {\r\n        return false;\r\n      }\r\n    } catch (e) {\r\n      console.error('Token decoding failed', e);\r\n      return false;\r\n    }\r\n\r\n    return true;\r\n  }\r\n\r\n  private decodeToken(token: string): any {\r\n    return jwtDecode(token);\r\n  }\r\n\r\n  private isTokenValid(decodedToken: any): boolean {\r\n    // Check if token has expired\r\n    const currentTime = Math.floor(Date.now() / 1000);\r\n    if (decodedToken.exp < currentTime) {\r\n      return false;\r\n    }\r\n\r\n    return true;\r\n  }\r\n\r\n\r\n\r\n  // ## -- Medicament API -- ##\r\n\r\n  /**\r\n   * Get medicament information and suggestions from OCR\r\n   * @param image Image file to analyze\r\n   * @param jobId Job ID for tracking progress\r\n   * @returns Promise with medicament suggestions\r\n   */\r\n  getMedicamentInfo(image: File, jobId: string): Observable<any> {\r\n    console.log('API Base URL:', this.baseUrl);\r\n\r\n    const formData: FormData = new FormData();\r\n    formData.append('image', image);\r\n    formData.append('job_id', jobId);  // Include the job ID\r\n    \r\n    const url = `${this.baseUrl}/medicament_ocr_tap/`;\r\n    console.log('Making request to:', url);\r\n    \r\n    return this.http.post<any>(url, formData);\r\n  }\r\n}\r\n"], "mappings": ";;AACA,SAAqBA,WAAW,QAA2B,sBAAsB;AACjF,SAAqBC,UAAU,QAAS,MAAM;AAC9C,SAASC,WAAW,QAAQ,gCAAgC;AAK5D,SAASC,UAAU,EAAEC,GAAG,QAAmB,gBAAgB;AAG3D,OAAOC,IAAI,MAAM,aAAa;AAC9B,SAASC,SAAS,QAAQ,YAAY;;;;AAEtC,IAAKC,OAGJ;AAHD,WAAKA,OAAO;EACVA,OAAA,yBAAqB;EACrBA,OAAA,uCAAmC;AACrC,CAAC,EAHIA,OAAO,KAAPA,OAAO;AAQZ,OAAM,MAAOC,UAAU;EAIrBC,YAAoBC,IAAgB,EAAUC,eAAgC;IAA1D,KAAAD,IAAI,GAAJA,IAAI;IAAsB,KAAAC,eAAe,GAAfA,eAAe;IAHrD,KAAAC,OAAO,GAAGV,WAAW,CAACW,MAAM,CAAC,CAAE;IAKvC;IACQ,KAAAC,WAAW,GAAG;MACpBC,OAAO,EAAE,IAAIf,WAAW,CAAC;QACvB,cAAc,EAAE;OACjB;KACF;EAPkF;EASnF;EACAgB,aAAaA,CAAA;IACX,OAAO,MAAM,GAAGC,IAAI,CAACC,MAAM,EAAE,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;EACzD;EAEQC,UAAUA,CAACC,GAAW,EAAEC,MAAc,EAAEC,IAAU;IACxDC,OAAO,CAACC,GAAG,CAAC,YAAYH,MAAM,IAAID,GAAG,EAAE,EAAEE,IAAI,CAAC;EAChD;EAEQG,WAAWA,CAACC,QAAa;IAC/BH,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEE,QAAQ,CAAC;EACpC;EAEQC,WAAWA,CAACC,KAAwB;IAC1CL,OAAO,CAACK,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;IAClC,OAAO7B,UAAU,CAAC6B,KAAK,CAAC;EAC1B;EAEA;EACAC,OAAOA,CAAA;IACL,OAAO,IAAI,CAACrB,IAAI,CAACsB,GAAG,CAAC,GAAG,IAAI,CAACpB,OAAO,GAAG,CAAC;EAC1C;EAEC;EACDqB,WAAWA,CAACC,OAA2B;IACrC,MAAMZ,GAAG,GAAG,GAAG,IAAI,CAACV,OAAO,eAAe;IAC1C,IAAI,CAACS,UAAU,CAACC,GAAG,EAAE,MAAM,EAAEY,OAAO,CAAC;IACrC,OAAO,IAAI,CAACxB,IAAI,CAACyB,IAAI,CAAsBb,GAAG,EAAEY,OAAO,EAAE,IAAI,CAACpB,WAAW,CAAC,CAACsB,IAAI,CAC7EhC,GAAG,CAAC,IAAI,CAACuB,WAAW,CAAC,EACrBxB,UAAU,CAAC,IAAI,CAAC0B,WAAW,CAAC,CAC7B;EACH;EAEA;EACAQ,SAASA,CAACH,OAAqB,EAAEI,WAAmB;IAClD,MAAMhB,GAAG,GAAG,GAAG,IAAI,CAACV,OAAO,QAAQ;IACnC,MAAMG,OAAO,GAAG,IAAIf,WAAW,CAAC;MAC9B,cAAc,EAAE,kBAAkB;MAClC,qBAAqB,EAAE,gBAAgBsC,WAAW;KACnD,CAAC;IACF,MAAMC,aAAa,GAAG;MAAE,GAAGL,OAAO;MAAEM,YAAY,EAAEF;IAAW,CAAE;IAC/D,IAAI,CAACjB,UAAU,CAACC,GAAG,EAAE,MAAM,EAAEiB,aAAa,CAAC;IAC3C,OAAO,IAAI,CAAC7B,IAAI,CAACyB,IAAI,CAAgBb,GAAG,EAAEiB,aAAa,EAAE;MAAExB;IAAO,CAAE,CAAC,CAACqB,IAAI,CACxEhC,GAAG,CAAC,IAAI,CAACuB,WAAW,CAAC,EACrBxB,UAAU,CAAC,IAAI,CAAC0B,WAAW,CAAC,CAC7B;EACH;EAEA;EACAY,eAAeA,CAACP,OAA+B;IAC7C,MAAMZ,GAAG,GAAG,GAAG,IAAI,CAACV,OAAO,mBAAmB;IAC9C,IAAI,CAACS,UAAU,CAACC,GAAG,EAAE,MAAM,EAAEY,OAAO,CAAC;IACrC,OAAO,IAAI,CAACxB,IAAI,CAACyB,IAAI,CAA0Bb,GAAG,EAAEY,OAAO,EAAE,IAAI,CAACpB,WAAW,CAAC,CAACsB,IAAI,CACjFhC,GAAG,CAAC,IAAI,CAACuB,WAAW,CAAC,EACrBxB,UAAU,CAAC,IAAI,CAAC0B,WAAW,CAAC,CAC7B;EACH;EAEA;EACMa,MAAMA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACV,OAAO,IAAIC,OAAO;QAAA,IAAAC,IAAA,GAAAF,iBAAA,CAAC,WAAOG,OAAO,EAAI;UACnC,MAAMC,KAAK,SAASL,KAAI,CAAChC,eAAe,CAACsC,MAAM,CAAC;YAC9CC,MAAM,EAAE,aAAa;YACrBC,OAAO,EAAE,4BAA4B;YACrCC,OAAO,EAAE,CACP;cACEC,IAAI,EAAE,SAAS;cACfC,IAAI,EAAE,QAAQ;cACdC,QAAQ,EAAE,4BAA4B;cACtCC,OAAO,EAAEA,CAAA,KAAK;gBACZ/B,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;gBAC7BqB,OAAO,EAAE,CAAC,CAAC;cACb;aACD,EACD;cACEM,IAAI,EAAE,KAAK;cACXE,QAAQ,EAAE,4BAA4B;cACtCC,OAAO,EAAEA,CAAA,KAAK;gBACZC,YAAY,CAACC,UAAU,CAAC,WAAW,CAAC;gBACpCD,YAAY,CAACC,UAAU,CAAC,aAAa,CAAC;gBACtCD,YAAY,CAACC,UAAU,CAAC,OAAO,CAAC;gBAChCD,YAAY,CAACC,UAAU,CAAC,SAAS,CAAC;gBAClCD,YAAY,CAACC,UAAU,CAAC,qBAAqB,CAAC;gBAC9CD,YAAY,CAACC,UAAU,CAAC,kBAAkB,CAAC;gBAC3CX,OAAO,EAAE,CAAC,CAAC;cACb;aACD;WAEJ,CAAC;UAEF,MAAMC,KAAK,CAACW,OAAO,EAAE;QACvB,CAAC;QAAA,iBAAAC,EAAA;UAAA,OAAAd,IAAA,CAAAe,KAAA,OAAAC,SAAA;QAAA;MAAA,IAAC;IAAC;EACL;EAEA;EACAC,SAASA,CAACC,KAAW,EAAEC,KAAa,EAAEC,SAAkB;IACtDzC,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,IAAI,CAACd,OAAO,CAAC;IAE1C,MAAMuD,QAAQ,GAAa,IAAIC,QAAQ,EAAE;IACzCD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAEL,KAAK,CAAC;IAC/BG,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAEJ,KAAK,CAAC,CAAC,CAAE;IACnCE,QAAQ,CAACE,MAAM,CAAC,WAAW,EAAEH,SAAS,CAAC/C,QAAQ,EAAE,CAAC,CAAC,CAAE;IACrD,MAAMG,GAAG,GAAG,GAAG,IAAI,CAACV,OAAO,cAAc;IACzC,IAAI,CAACS,UAAU,CAACC,GAAG,EAAE,MAAM,EAAE6C,QAAQ,CAAC;IACtC,OAAO,IAAI,CAACzD,IAAI,CAACyB,IAAI,CAAMb,GAAG,EAAE6C,QAAQ,CAAC,CAAC/B,IAAI,CAC5ChC,GAAG,CAAC,IAAI,CAACuB,WAAW,CAAC,EACrBxB,UAAU,CAAC,IAAI,CAAC0B,WAAW,CAAC,CAC7B;EACH;EAEA;EACAyC,cAAcA,CAACN,KAAW,EAAEO,SAAiB,EAAEC,QAAiB,EAAEP,KAAc;IAC9E,MAAME,QAAQ,GAAa,IAAIC,QAAQ,EAAE;IACzCD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAEL,KAAK,CAAC;IAC/BG,QAAQ,CAACE,MAAM,CAAC,YAAY,EAAEE,SAAS,CAAC;IACxCJ,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAEJ,KAAK,aAALA,KAAK,cAALA,KAAK,GAAI,EAAE,CAAC,CAAC,CAAE;IACzC,IAAIO,QAAQ,EAAE;MACZL,QAAQ,CAACE,MAAM,CAAC,WAAW,EAAEG,QAAQ,CAAC;;IAGxC,OAAO,IAAI,CAAC9D,IAAI,CAACyB,IAAI,CAAM,GAAG,IAAI,CAACvB,OAAO,oBAAoB,EAAEuD,QAAQ,CAAC;EAC3E;EAEA;EACAM,gBAAgBA,CAACT,KAAW,EAAEO,SAAiB,EAAEC,QAAiB,EAAEP,KAAc;IAChF,MAAME,QAAQ,GAAa,IAAIC,QAAQ,EAAE;IACzCD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAEL,KAAK,CAAC;IAC/BG,QAAQ,CAACE,MAAM,CAAC,YAAY,EAAEE,SAAS,CAAC;IACxCJ,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAEJ,KAAK,aAALA,KAAK,cAALA,KAAK,GAAI,EAAE,CAAC,CAAC,CAAE;IACzC,IAAIO,QAAQ,EAAE;MACZL,QAAQ,CAACE,MAAM,CAAC,WAAW,EAAEG,QAAQ,CAAC;;IAGxC,OAAO,IAAI,CAAC9D,IAAI,CAACyB,IAAI,CAAM,GAAG,IAAI,CAACvB,OAAO,qBAAqB,EAAEuD,QAAQ,CAAC;EAC5E;EAEA;EACAO,gBAAgBA,CAACxC,OAAgC,EAAE+B,KAAa;IAC9D;IACA,MAAMU,eAAe,GAAG;MAAE,GAAGzC,OAAO;MAAE0C,MAAM,EAAEX;IAAK,CAAE,CAAC,CAAE;IACxD,OAAO,IAAI,CAACvD,IAAI,CAACyB,IAAI,CAAM,GAAG,IAAI,CAACvB,OAAO,sBAAsB,EAAE+D,eAAe,EAAE,IAAI,CAAC7D,WAAW,CAAC;EACtG;EAEA;EACA+D,eAAeA,CAACC,MAAmB,EAAEb,KAAa,EAAEc,SAAiB,EAAEC,OAAA,GAAmBzE,OAAO,CAAC0E,QAAQ;IACxG,MAAMC,cAAc,GAAGJ,MAAM,CAACK,GAAG,CAACnB,KAAK,KAAK;MAAE,GAAGA,KAAK;MAAEY,MAAM,EAAEX;IAAK,CAAE,CAAC,CAAC;IAEzE;IACA,MAAMmB,WAAW,GAAG;MAClBN,MAAM,EAAEI,cAAc;MACtBG,QAAQ,EAAEL,OAAO,CAACM,WAAW,EAAE,CAAC;KACjC;IAED,OAAO,IAAI,CAAC5E,IAAI,CAACyB,IAAI,CAAC,GAAG,IAAI,CAACvB,OAAO,qBAAqB,EAAEwE,WAAW,EAAE,IAAI,CAACtE,WAAW,CAAC;EAC5F;EAEA;EACAyE,cAAcA,CAACC,IAAY,EAAEC,MAAc,EAAEC,aAAqB,EAAEC,eAAuB,EAAEC,aAAqB;IAChH,OAAO,IAAI,CAAClF,IAAI,CAACmF,GAAG,CAAC,GAAG,IAAI,CAACjF,OAAO,yBAAyB4E,IAAI,EAAE,EAAE;MAAEC,MAAM;MAAEC,aAAa;MAAEC,eAAe;MAAEC;IAAa,CAAE,CAAC;EACjI;EAEA;EACAE,eAAeA,CAAA;IACb,OAAO,IAAI,CAACpF,IAAI,CAACsB,GAAG,CAAC,GAAG,IAAI,CAACpB,OAAO,YAAY,CAAC;EACnD;EAEAmF,cAAcA,CAAC5C,OAAe;IAC5B,MAAM6C,gBAAgB,GAAG7C,OAAO,KAAK,EAAE,GAAGA,OAAO,GAAG,oFAAoF;IAExI9C,IAAI,CAAC4F,IAAI,CAAC;MACRC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,iCAAiC;MACxCC,IAAI,EAAEJ,gBAAgB;MACtBK,MAAM,EAAE,8DAA8D;MACtEC,iBAAiB,EAAE,KAAK;MACxBC,eAAe,EAAE,IAAI;MACrBC,WAAW,EAAE;QACXC,WAAW,EAAE,qBAAqB;QAClCC,KAAK,EAAE,cAAc;QACrBL,MAAM,EAAE,eAAe,CAAc;;KAExC,CAAC;IAEF;IACA;IACA5C,YAAY,CAACC,UAAU,CAAC,kBAAkB,CAAC;EAG7C;EAEAiD,UAAUA,CAAA;IACR,MAAMC,KAAK,GAAGnD,YAAY,CAACoD,OAAO,CAAC,OAAO,CAAC;IAC3C,MAAMC,SAAS,GAAGrD,YAAY,CAACoD,OAAO,CAAC,WAAW,CAAC;IACnD,MAAME,WAAW,GAAGtD,YAAY,CAACoD,OAAO,CAAC,aAAa,CAAC;IAEvD,IAAI,CAACD,KAAK,IAAI,CAACE,SAAS,IAAI,CAACC,WAAW,EAAE;MACxC,OAAO,KAAK;;IAGd,IAAI;MACF,MAAMC,YAAY,GAAG,IAAI,CAACC,WAAW,CAACL,KAAK,CAAC;MAC5C,IAAI,CAAC,IAAI,CAACM,YAAY,CAACF,YAAY,CAAC,EAAE;QACpC,OAAO,KAAK;;KAEf,CAAC,OAAOG,CAAC,EAAE;MACV1F,OAAO,CAACK,KAAK,CAAC,uBAAuB,EAAEqF,CAAC,CAAC;MACzC,OAAO,KAAK;;IAGd,OAAO,IAAI;EACb;EAEQF,WAAWA,CAACL,KAAa;IAC/B,OAAOtG,SAAS,CAACsG,KAAK,CAAC;EACzB;EAEQM,YAAYA,CAACF,YAAiB;IACpC;IACA,MAAMI,WAAW,GAAGnG,IAAI,CAACoG,KAAK,CAACC,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,CAAC;IACjD,IAAIP,YAAY,CAACQ,GAAG,GAAGJ,WAAW,EAAE;MAClC,OAAO,KAAK;;IAGd,OAAO,IAAI;EACb;EAIA;EAEA;;;;;;EAMAK,iBAAiBA,CAACzD,KAAW,EAAEC,KAAa;IAC1CxC,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,IAAI,CAACd,OAAO,CAAC;IAE1C,MAAMuD,QAAQ,GAAa,IAAIC,QAAQ,EAAE;IACzCD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAEL,KAAK,CAAC;IAC/BG,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAEJ,KAAK,CAAC,CAAC,CAAE;IAEnC,MAAM3C,GAAG,GAAG,GAAG,IAAI,CAACV,OAAO,sBAAsB;IACjDa,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEJ,GAAG,CAAC;IAEtC,OAAO,IAAI,CAACZ,IAAI,CAACyB,IAAI,CAAMb,GAAG,EAAE6C,QAAQ,CAAC;EAC3C;;cApQW3D,UAAU;;mBAAVA,WAAU,EAAAkH,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,eAAA;AAAA;;SAAVvH,WAAU;EAAAwH,OAAA,EAAVxH,WAAU,CAAAyH,IAAA;EAAAC,UAAA,EAFT;AAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}