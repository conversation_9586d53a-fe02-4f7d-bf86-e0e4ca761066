{"ast": null, "code": "var _WelcomePage;\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ionic/angular\";\nexport class WelcomePage {\n  constructor(navCtrl) {\n    this.navCtrl = navCtrl;\n  }\n  ngOnInit() {\n    console.log(\"Welcome Page\");\n  }\n  selectPlatform(platform) {\n    // Store the selected platform in localStorage\n    localStorage.setItem('src_app', platform);\n    console.log('Platform selected:', platform);\n    // Navigate to login page\n    this.navCtrl.navigateRoot('/login');\n  }\n}\n_WelcomePage = WelcomePage;\n_WelcomePage.ɵfac = function WelcomePage_Factory(t) {\n  return new (t || _WelcomePage)(i0.ɵɵdirectiveInject(i1.NavController));\n};\n_WelcomePage.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: _WelcomePage,\n  selectors: [[\"app-welcome\"]],\n  decls: 19,\n  vars: 0,\n  consts: [[1, \"welcome-wrapper\"], [\"size\", \"12\", 1, \"slide-content\"], [\"src\", \"/assets/icon-welcome.svg\", 1, \"slide-image\"], [1, \"content-slide\"], [1, \"platform-selection\"], [\"fill\", \"clear\", 1, \"platform-button\", \"winplus-button\", 3, \"click\"], [\"src\", \"/assets/onboarding_images/winpluspharm.svg\", \"alt\", \"WinPlusPharm\", 1, \"platform-logo\"], [\"fill\", \"clear\", 1, \"platform-button\", \"pharmalien-button\", 3, \"click\"], [\"src\", \"/assets/onboarding_images/winpharm.svg\", \"alt\", \"Pharmalien\", 1, \"platform-logo\"]],\n  template: function WelcomePage_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelement(0, \"ion-header\");\n      i0.ɵɵelementStart(1, \"ion-content\")(2, \"div\", 0)(3, \"ion-row\")(4, \"ion-col\", 1);\n      i0.ɵɵelement(5, \"img\", 2);\n      i0.ɵɵelementStart(6, \"div\", 3)(7, \"h2\");\n      i0.ɵɵtext(8, \"Scannez ,r\\u00E9cup\\u00E9rez, automatisez !\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(9, \"p\");\n      i0.ɵɵtext(10, \"Vos bons de livraison enregistr\\u00E9s automatiquement en un instant.\");\n      i0.ɵɵelementEnd()()()()()();\n      i0.ɵɵelementStart(11, \"ion-footer\")(12, \"h2\");\n      i0.ɵɵtext(13, \"Choisissez votre plateforme\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(14, \"div\", 4)(15, \"ion-button\", 5);\n      i0.ɵɵlistener(\"click\", function WelcomePage_Template_ion_button_click_15_listener() {\n        return ctx.selectPlatform(\"winpluspharma\");\n      });\n      i0.ɵɵelement(16, \"img\", 6);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(17, \"ion-button\", 7);\n      i0.ɵɵlistener(\"click\", function WelcomePage_Template_ion_button_click_17_listener() {\n        return ctx.selectPlatform(\"pharmalien\");\n      });\n      i0.ɵɵelement(18, \"img\", 8);\n      i0.ɵɵelementEnd()()();\n    }\n  },\n  dependencies: [i1.IonButton, i1.IonCol, i1.IonContent, i1.IonFooter, i1.IonHeader, i1.IonRow],\n  styles: [\"@import url(https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap);@import url(https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0[_ngcontent-%COMP%], 200[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 300[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 400[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 500[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 600[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 700[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 800[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 900[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 100[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 200[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 300[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 400[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 500[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 600[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 700[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 800[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 900&display=swap)[_ngcontent-%COMP%];*[_ngcontent-%COMP%] {\\n  font-family: \\\"Inter\\\", sans-serif;\\n  font-optical-sizing: auto;\\n}\\n\\nion-content[_ngcontent-%COMP%]::part(scroll) {\\n  overflow-y: auto;\\n}\\n\\nion-button[_ngcontent-%COMP%]::part(native) {\\n  --padding-top: 20px !important;\\n  --padding-bottom: 20px !important;\\n}\\n\\n.platform-selection[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-around;\\n  align-items: center;\\n  padding: 20px;\\n  gap: 20px;\\n}\\n\\n.platform-button[_ngcontent-%COMP%] {\\n  flex: 1;\\n  height: 80px;\\n  --border-radius: 12px;\\n  --box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n  margin: 0;\\n}\\n\\n.platform-button[_ngcontent-%COMP%]::part(native) {\\n  padding: 0;\\n  background: white;\\n  border: 2px solid #e0e0e0;\\n  border-radius: 12px;\\n  transition: all 0.3s ease;\\n}\\n\\n.platform-button[_ngcontent-%COMP%]:hover::part(native) {\\n  border-color: #007bff;\\n  transform: translateY(-2px);\\n  box-shadow: 0 6px 16px rgba(0, 123, 255, 0.2);\\n}\\n\\n.platform-logo[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 60px;\\n  object-fit: contain;\\n  padding: 10px;\\n}\\n\\n.welcome-wrapper[_ngcontent-%COMP%] {\\n  background: url(\\\"/assets/bg-welcome.png\\\") no-repeat center center fixed;\\n  background-size: cover;\\n  height: 100%;\\n}\\n\\nion-footer[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n  padding: 10px;\\n  display: flex;\\n  justify-content: center;\\n  flex-direction: column;\\n  align-items: center;\\n}\\n\\nion-toolbar[_ngcontent-%COMP%] {\\n  --background: transparent;\\n  --ion-color-primary: #2f4fcd;\\n}\\n\\nion-button[_ngcontent-%COMP%] {\\n  margin: 10px;\\n}\\n\\nion-button.welcome-button[_ngcontent-%COMP%] {\\n  --background: #1f41bb;\\n  --background-activated: #1f41bb;\\n  --border-radius: 8px;\\n  width: 65%;\\n  text-align: center;\\n  box-shadow: 0px 16px 20px rgb(203, 214, 255); \\n\\n  color: #fff;\\n  font-size: 20px;\\n  font-weight: bold;\\n}\\n\\nion-row[_ngcontent-%COMP%] {\\n  height: 100%;\\n  height: 85vh;\\n}\\n\\n  ion-row ion-col {\\n  padding-bottom: 0 !important;\\n}\\n\\n  ion-col {\\n  display: flex !important;\\n  flex-direction: column;\\n  justify-content: space-evenly;\\n  align-items: center;\\n}\\n\\n  img {\\n  width: auto;\\n  max-width: 100%;\\n  height: auto;\\n  max-height: 100%;\\n}\\n\\n  .content-slide {\\n  text-align: left;\\n  padding: 0 20px;\\n}\\n\\n  .content-slide h2 {\\n  font-family: \\\"Poppins\\\", \\\"Inter\\\", sans-serif !important;\\n  font-weight: bold;\\n  font-style: normal;\\n  font-size: 30px;\\n  color: #1f41bb;\\n}\\n\\n  ion-footer h2 {\\n  font-family: \\\"Poppins\\\", \\\"Inter\\\", sans-serif !important;\\n  font-weight: bold;\\n  font-style: normal;\\n  font-size: 20px;\\n  color: #1f41bb;\\n  text-align: center;\\n}\\n\\n  .content-slide p {\\n  padding-right: 20px;\\n  margin-top: 40px;\\n  letter-spacing: 1.1px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvd2VsY29tZS93ZWxjb21lLnBhZ2Uuc2NzcyIsIndlYnBhY2s6Ly8uLy4uLy4uLy4uLy4uL1dvcmslMjBfX0FiZGVycmFobWFuZV9vdWhuYS9PQ1JfRE9DVU1FTlRfR1JPU1NJU1RFL0Zyb250ZW5kJTIwb2NyJTIwZ3Jvc3Npc3RlJTIwZG9jdW1lbnQvZnJvbnRlbmRfb2NyX2dyb3NzaXN0ZV9kb2N1bWVudC9zcmMvYXBwL3dlbGNvbWUvd2VsY29tZS5wYWdlLnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBR0E7RUFDRSxnQ0FBQTtFQUNBLHlCQUFBO0FDQUY7O0FER0E7RUFJRSxnQkFBQTtBQ0hGOztBRE1BO0VBQ0UsOEJBQUE7RUFDQSxpQ0FBQTtBQ0hGOztBRE1BO0VBQ0UsYUFBQTtFQUNBLDZCQUFBO0VBQ0EsbUJBQUE7RUFDQSxhQUFBO0VBQ0EsU0FBQTtBQ0hGOztBRE1BO0VBQ0UsT0FBQTtFQUNBLFlBQUE7RUFDQSxxQkFBQTtFQUNBLDJDQUFBO0VBQ0EsU0FBQTtBQ0hGOztBRE1BO0VBQ0UsVUFBQTtFQUNBLGlCQUFBO0VBQ0EseUJBQUE7RUFDQSxtQkFBQTtFQUNBLHlCQUFBO0FDSEY7O0FETUE7RUFDRSxxQkFBQTtFQUNBLDJCQUFBO0VBQ0EsNkNBQUE7QUNIRjs7QURNQTtFQUNFLFdBQUE7RUFDQSxZQUFBO0VBQ0EsbUJBQUE7RUFDQSxhQUFBO0FDSEY7O0FES0E7RUFDRSx1RUFBQTtFQUNBLHNCQUFBO0VBQ0EsWUFBQTtBQ0ZGOztBREtBO0VBQ0Usa0JBQUE7RUFFQSxXQUFBO0VBQ0EsYUFBQTtFQUNBLGFBQUE7RUFDQSx1QkFBQTtFQUNBLHNCQUFBO0VBQ0EsbUJBQUE7QUNIRjs7QURNQTtFQUNFLHlCQUFBO0VBQ0EsNEJBQUE7QUNIRjs7QURNQTtFQUNFLFlBQUE7QUNIRjs7QURNQTtFQUNFLHFCQUFBO0VBQ0EsK0JBQUE7RUFDQSxvQkFBQTtFQUNBLFVBQUE7RUFDQSxrQkFBQTtFQUNBLDRDQUFBLEVBQUEsZUFBQTtFQUNBLFdBQUE7RUFDQSxlQUFBO0VBQ0EsaUJBQUE7QUNIRjs7QURNQTtFQUNFLFlBQUE7RUFDQSxZQUFBO0FDSEY7O0FETUE7RUFFRSw0QkFBQTtBQ0pGOztBRE9BO0VBQ0Usd0JBQUE7RUFDQSxzQkFBQTtFQUNBLDZCQUFBO0VBQ0EsbUJBQUE7QUNKRjs7QURPQTtFQUNFLFdBQUE7RUFDQSxlQUFBO0VBQ0EsWUFBQTtFQUNBLGdCQUFBO0FDSkY7O0FET0E7RUFDRSxnQkFBQTtFQUNBLGVBQUE7QUNKRjs7QURPQTtFQUNFLHNEQUFBO0VBQ0EsaUJBQUE7RUFDQSxrQkFBQTtFQUNBLGVBQUE7RUFDQSxjQUFBO0FDSkY7O0FET0E7RUFDRSxzREFBQTtFQUNBLGlCQUFBO0VBQ0Esa0JBQUE7RUFDQSxlQUFBO0VBQ0EsY0FBQTtFQUNBLGtCQUFBO0FDSkY7O0FET0E7RUFDRSxtQkFBQTtFQUNBLGdCQUFBO0VBQ0EscUJBQUE7QUNKRiIsInNvdXJjZXNDb250ZW50IjpbIkBpbXBvcnQgdXJsKFwiaHR0cHM6Ly9mb250cy5nb29nbGVhcGlzLmNvbS9jc3MyP2ZhbWlseT1JbnRlcjp3Z2h0QDEwMC4uOTAwJmRpc3BsYXk9c3dhcFwiKTtcclxuQGltcG9ydCB1cmwoXCJodHRwczovL2ZvbnRzLmdvb2dsZWFwaXMuY29tL2NzczI/ZmFtaWx5PVBvcHBpbnM6aXRhbCx3Z2h0QDAsMTAwOzAsMjAwOzAsMzAwOzAsNDAwOzAsNTAwOzAsNjAwOzAsNzAwOzAsODAwOzAsOTAwOzEsMTAwOzEsMjAwOzEsMzAwOzEsNDAwOzEsNTAwOzEsNjAwOzEsNzAwOzEsODAwOzEsOTAwJmRpc3BsYXk9c3dhcFwiKTtcclxuXHJcbioge1xyXG4gIGZvbnQtZmFtaWx5OiBcIkludGVyXCIsIHNhbnMtc2VyaWY7XHJcbiAgZm9udC1vcHRpY2FsLXNpemluZzogYXV0bztcclxufVxyXG5cclxuaW9uLWNvbnRlbnQ6OnBhcnQoc2Nyb2xsKSB7XHJcbiAgLy8gb3ZlcmZsb3cteTogaGlkZGVuICFpbXBvcnRhbnQ7XHJcbiAgLy8gLS1vdmVyZmxvdzogaGlkZGVuICFpbXBvcnRhbnQ7XHJcblxyXG4gIG92ZXJmbG93LXk6IGF1dG87XHJcbn1cclxuXHJcbmlvbi1idXR0b246OnBhcnQobmF0aXZlKSB7XHJcbiAgLS1wYWRkaW5nLXRvcCA6IDIwcHggIWltcG9ydGFudDtcclxuICAtLXBhZGRpbmctYm90dG9tIDogMjBweCAhaW1wb3J0YW50O1xyXG59XHJcblxyXG4ucGxhdGZvcm0tc2VsZWN0aW9uIHtcclxuICBkaXNwbGF5OiBmbGV4O1xyXG4gIGp1c3RpZnktY29udGVudDogc3BhY2UtYXJvdW5kO1xyXG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgcGFkZGluZzogMjBweDtcclxuICBnYXA6IDIwcHg7XHJcbn1cclxuXHJcbi5wbGF0Zm9ybS1idXR0b24ge1xyXG4gIGZsZXg6IDE7XHJcbiAgaGVpZ2h0OiA4MHB4O1xyXG4gIC0tYm9yZGVyLXJhZGl1czogMTJweDtcclxuICAtLWJveC1zaGFkb3c6IDAgNHB4IDEycHggcmdiYSgwLCAwLCAwLCAwLjEpO1xyXG4gIG1hcmdpbjogMDtcclxufVxyXG5cclxuLnBsYXRmb3JtLWJ1dHRvbjo6cGFydChuYXRpdmUpIHtcclxuICBwYWRkaW5nOiAwO1xyXG4gIGJhY2tncm91bmQ6IHdoaXRlO1xyXG4gIGJvcmRlcjogMnB4IHNvbGlkICNlMGUwZTA7XHJcbiAgYm9yZGVyLXJhZGl1czogMTJweDtcclxuICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xyXG59XHJcblxyXG4ucGxhdGZvcm0tYnV0dG9uOmhvdmVyOjpwYXJ0KG5hdGl2ZSkge1xyXG4gIGJvcmRlci1jb2xvcjogIzAwN2JmZjtcclxuICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTJweCk7XHJcbiAgYm94LXNoYWRvdzogMCA2cHggMTZweCByZ2JhKDAsIDEyMywgMjU1LCAwLjIpO1xyXG59XHJcblxyXG4ucGxhdGZvcm0tbG9nbyB7XHJcbiAgd2lkdGg6IDEwMCU7XHJcbiAgaGVpZ2h0OiA2MHB4O1xyXG4gIG9iamVjdC1maXQ6IGNvbnRhaW47XHJcbiAgcGFkZGluZzogMTBweDtcclxufVxyXG4ud2VsY29tZS13cmFwcGVyIHtcclxuICBiYWNrZ3JvdW5kOiB1cmwoXCIvYXNzZXRzL2JnLXdlbGNvbWUucG5nXCIpIG5vLXJlcGVhdCBjZW50ZXIgY2VudGVyIGZpeGVkO1xyXG4gIGJhY2tncm91bmQtc2l6ZTogY292ZXI7XHJcbiAgaGVpZ2h0OiAxMDAlO1xyXG59XHJcblxyXG5pb24tZm9vdGVyIHtcclxuICBwb3NpdGlvbjogcmVsYXRpdmU7XHJcbiAgLy8gYm90dG9tOiAtMzBweDtcclxuICB3aWR0aDogMTAwJTtcclxuICBwYWRkaW5nOiAxMHB4O1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XHJcbiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcclxuICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG59XHJcblxyXG5pb24tdG9vbGJhciB7XHJcbiAgLS1iYWNrZ3JvdW5kOiB0cmFuc3BhcmVudDtcclxuICAtLWlvbi1jb2xvci1wcmltYXJ5OiAjMmY0ZmNkO1xyXG59XHJcblxyXG5pb24tYnV0dG9uIHtcclxuICBtYXJnaW46IDEwcHg7XHJcbn1cclxuXHJcbmlvbi1idXR0b24ud2VsY29tZS1idXR0b24ge1xyXG4gIC0tYmFja2dyb3VuZDogIzFmNDFiYjtcclxuICAtLWJhY2tncm91bmQtYWN0aXZhdGVkOiAjMWY0MWJiO1xyXG4gIC0tYm9yZGVyLXJhZGl1czogOHB4O1xyXG4gIHdpZHRoOiA2NSU7XHJcbiAgdGV4dC1hbGlnbjogY2VudGVyO1xyXG4gIGJveC1zaGFkb3c6IDBweCAxNnB4IDIwcHggcmdiYSgyMDMsIDIxNCwgMjU1LCAxKTsgLyogQWRkIHNoYWRvdyAqL1xyXG4gIGNvbG9yOiAjZmZmO1xyXG4gIGZvbnQtc2l6ZTogMjBweDtcclxuICBmb250LXdlaWdodDogYm9sZDtcclxufVxyXG5cclxuaW9uLXJvdyB7XHJcbiAgaGVpZ2h0OiAxMDAlO1xyXG4gIGhlaWdodDogODV2aDtcclxufVxyXG5cclxuOjpuZy1kZWVwIGlvbi1yb3cgaW9uLWNvbCB7XHJcbiAgLy8gICBwYWRkaW5nLXRvcDogMCAhaW1wb3J0YW50O1xyXG4gIHBhZGRpbmctYm90dG9tOiAwICFpbXBvcnRhbnQ7XHJcbn1cclxuXHJcbjo6bmctZGVlcCBpb24tY29sIHtcclxuICBkaXNwbGF5OiBmbGV4ICFpbXBvcnRhbnQ7XHJcbiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcclxuICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWV2ZW5seTtcclxuICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG59XHJcblxyXG46Om5nLWRlZXAgaW1nIHtcclxuICB3aWR0aDogYXV0bztcclxuICBtYXgtd2lkdGg6IDEwMCU7XHJcbiAgaGVpZ2h0OiBhdXRvO1xyXG4gIG1heC1oZWlnaHQ6IDEwMCU7XHJcbn1cclxuXHJcbjo6bmctZGVlcCAuY29udGVudC1zbGlkZSB7XHJcbiAgdGV4dC1hbGlnbjogbGVmdDtcclxuICBwYWRkaW5nOiAwIDIwcHg7XHJcbn1cclxuXHJcbjo6bmctZGVlcCAuY29udGVudC1zbGlkZSBoMiB7XHJcbiAgZm9udC1mYW1pbHk6IFwiUG9wcGluc1wiLCBcIkludGVyXCIsIHNhbnMtc2VyaWYgIWltcG9ydGFudDtcclxuICBmb250LXdlaWdodDogYm9sZDtcclxuICBmb250LXN0eWxlOiBub3JtYWw7XHJcbiAgZm9udC1zaXplOiAzMHB4O1xyXG4gIGNvbG9yOiAjMWY0MWJiO1xyXG59XHJcblxyXG46Om5nLWRlZXAgaW9uLWZvb3RlciBoMiB7XHJcbiAgZm9udC1mYW1pbHk6IFwiUG9wcGluc1wiLCBcIkludGVyXCIsIHNhbnMtc2VyaWYgIWltcG9ydGFudDtcclxuICBmb250LXdlaWdodDogYm9sZDtcclxuICBmb250LXN0eWxlOiBub3JtYWw7XHJcbiAgZm9udC1zaXplOiAyMHB4O1xyXG4gIGNvbG9yOiAjMWY0MWJiO1xyXG4gIHRleHQtYWxpZ246IGNlbnRlcjtcclxufVxyXG5cclxuOjpuZy1kZWVwIC5jb250ZW50LXNsaWRlIHAge1xyXG4gIHBhZGRpbmctcmlnaHQ6IDIwcHg7XHJcbiAgbWFyZ2luLXRvcDogNDBweDtcclxuICBsZXR0ZXItc3BhY2luZzogMS4xcHg7XHJcbn1cclxuIiwiQGltcG9ydCB1cmwoXCJodHRwczovL2ZvbnRzLmdvb2dsZWFwaXMuY29tL2NzczI/ZmFtaWx5PUludGVyOndnaHRAMTAwLi45MDAmZGlzcGxheT1zd2FwXCIpO1xuQGltcG9ydCB1cmwoXCJodHRwczovL2ZvbnRzLmdvb2dsZWFwaXMuY29tL2NzczI/ZmFtaWx5PVBvcHBpbnM6aXRhbCx3Z2h0QDAsMTAwOzAsMjAwOzAsMzAwOzAsNDAwOzAsNTAwOzAsNjAwOzAsNzAwOzAsODAwOzAsOTAwOzEsMTAwOzEsMjAwOzEsMzAwOzEsNDAwOzEsNTAwOzEsNjAwOzEsNzAwOzEsODAwOzEsOTAwJmRpc3BsYXk9c3dhcFwiKTtcbioge1xuICBmb250LWZhbWlseTogXCJJbnRlclwiLCBzYW5zLXNlcmlmO1xuICBmb250LW9wdGljYWwtc2l6aW5nOiBhdXRvO1xufVxuXG5pb24tY29udGVudDo6cGFydChzY3JvbGwpIHtcbiAgb3ZlcmZsb3cteTogYXV0bztcbn1cblxuaW9uLWJ1dHRvbjo6cGFydChuYXRpdmUpIHtcbiAgLS1wYWRkaW5nLXRvcDogMjBweCAhaW1wb3J0YW50O1xuICAtLXBhZGRpbmctYm90dG9tOiAyMHB4ICFpbXBvcnRhbnQ7XG59XG5cbi5wbGF0Zm9ybS1zZWxlY3Rpb24ge1xuICBkaXNwbGF5OiBmbGV4O1xuICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWFyb3VuZDtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgcGFkZGluZzogMjBweDtcbiAgZ2FwOiAyMHB4O1xufVxuXG4ucGxhdGZvcm0tYnV0dG9uIHtcbiAgZmxleDogMTtcbiAgaGVpZ2h0OiA4MHB4O1xuICAtLWJvcmRlci1yYWRpdXM6IDEycHg7XG4gIC0tYm94LXNoYWRvdzogMCA0cHggMTJweCByZ2JhKDAsIDAsIDAsIDAuMSk7XG4gIG1hcmdpbjogMDtcbn1cblxuLnBsYXRmb3JtLWJ1dHRvbjo6cGFydChuYXRpdmUpIHtcbiAgcGFkZGluZzogMDtcbiAgYmFja2dyb3VuZDogd2hpdGU7XG4gIGJvcmRlcjogMnB4IHNvbGlkICNlMGUwZTA7XG4gIGJvcmRlci1yYWRpdXM6IDEycHg7XG4gIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7XG59XG5cbi5wbGF0Zm9ybS1idXR0b246aG92ZXI6OnBhcnQobmF0aXZlKSB7XG4gIGJvcmRlci1jb2xvcjogIzAwN2JmZjtcbiAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0ycHgpO1xuICBib3gtc2hhZG93OiAwIDZweCAxNnB4IHJnYmEoMCwgMTIzLCAyNTUsIDAuMik7XG59XG5cbi5wbGF0Zm9ybS1sb2dvIHtcbiAgd2lkdGg6IDEwMCU7XG4gIGhlaWdodDogNjBweDtcbiAgb2JqZWN0LWZpdDogY29udGFpbjtcbiAgcGFkZGluZzogMTBweDtcbn1cblxuLndlbGNvbWUtd3JhcHBlciB7XG4gIGJhY2tncm91bmQ6IHVybChcIi9hc3NldHMvYmctd2VsY29tZS5wbmdcIikgbm8tcmVwZWF0IGNlbnRlciBjZW50ZXIgZml4ZWQ7XG4gIGJhY2tncm91bmQtc2l6ZTogY292ZXI7XG4gIGhlaWdodDogMTAwJTtcbn1cblxuaW9uLWZvb3RlciB7XG4gIHBvc2l0aW9uOiByZWxhdGl2ZTtcbiAgd2lkdGg6IDEwMCU7XG4gIHBhZGRpbmc6IDEwcHg7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xufVxuXG5pb24tdG9vbGJhciB7XG4gIC0tYmFja2dyb3VuZDogdHJhbnNwYXJlbnQ7XG4gIC0taW9uLWNvbG9yLXByaW1hcnk6ICMyZjRmY2Q7XG59XG5cbmlvbi1idXR0b24ge1xuICBtYXJnaW46IDEwcHg7XG59XG5cbmlvbi1idXR0b24ud2VsY29tZS1idXR0b24ge1xuICAtLWJhY2tncm91bmQ6ICMxZjQxYmI7XG4gIC0tYmFja2dyb3VuZC1hY3RpdmF0ZWQ6ICMxZjQxYmI7XG4gIC0tYm9yZGVyLXJhZGl1czogOHB4O1xuICB3aWR0aDogNjUlO1xuICB0ZXh0LWFsaWduOiBjZW50ZXI7XG4gIGJveC1zaGFkb3c6IDBweCAxNnB4IDIwcHggcmdiKDIwMywgMjE0LCAyNTUpOyAvKiBBZGQgc2hhZG93ICovXG4gIGNvbG9yOiAjZmZmO1xuICBmb250LXNpemU6IDIwcHg7XG4gIGZvbnQtd2VpZ2h0OiBib2xkO1xufVxuXG5pb24tcm93IHtcbiAgaGVpZ2h0OiAxMDAlO1xuICBoZWlnaHQ6IDg1dmg7XG59XG5cbjo6bmctZGVlcCBpb24tcm93IGlvbi1jb2wge1xuICBwYWRkaW5nLWJvdHRvbTogMCAhaW1wb3J0YW50O1xufVxuXG46Om5nLWRlZXAgaW9uLWNvbCB7XG4gIGRpc3BsYXk6IGZsZXggIWltcG9ydGFudDtcbiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1ldmVubHk7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG59XG5cbjo6bmctZGVlcCBpbWcge1xuICB3aWR0aDogYXV0bztcbiAgbWF4LXdpZHRoOiAxMDAlO1xuICBoZWlnaHQ6IGF1dG87XG4gIG1heC1oZWlnaHQ6IDEwMCU7XG59XG5cbjo6bmctZGVlcCAuY29udGVudC1zbGlkZSB7XG4gIHRleHQtYWxpZ246IGxlZnQ7XG4gIHBhZGRpbmc6IDAgMjBweDtcbn1cblxuOjpuZy1kZWVwIC5jb250ZW50LXNsaWRlIGgyIHtcbiAgZm9udC1mYW1pbHk6IFwiUG9wcGluc1wiLCBcIkludGVyXCIsIHNhbnMtc2VyaWYgIWltcG9ydGFudDtcbiAgZm9udC13ZWlnaHQ6IGJvbGQ7XG4gIGZvbnQtc3R5bGU6IG5vcm1hbDtcbiAgZm9udC1zaXplOiAzMHB4O1xuICBjb2xvcjogIzFmNDFiYjtcbn1cblxuOjpuZy1kZWVwIGlvbi1mb290ZXIgaDIge1xuICBmb250LWZhbWlseTogXCJQb3BwaW5zXCIsIFwiSW50ZXJcIiwgc2Fucy1zZXJpZiAhaW1wb3J0YW50O1xuICBmb250LXdlaWdodDogYm9sZDtcbiAgZm9udC1zdHlsZTogbm9ybWFsO1xuICBmb250LXNpemU6IDIwcHg7XG4gIGNvbG9yOiAjMWY0MWJiO1xuICB0ZXh0LWFsaWduOiBjZW50ZXI7XG59XG5cbjo6bmctZGVlcCAuY29udGVudC1zbGlkZSBwIHtcbiAgcGFkZGluZy1yaWdodDogMjBweDtcbiAgbWFyZ2luLXRvcDogNDBweDtcbiAgbGV0dGVyLXNwYWNpbmc6IDEuMXB4O1xufSJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n});", "map": {"version": 3, "names": ["WelcomePage", "constructor", "navCtrl", "ngOnInit", "console", "log", "selectPlatform", "platform", "localStorage", "setItem", "navigateRoot", "i0", "ɵɵdirectiveInject", "i1", "NavController", "selectors", "decls", "vars", "consts", "template", "WelcomePage_Template", "rf", "ctx", "ɵɵelement", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "WelcomePage_Template_ion_button_click_15_listener", "WelcomePage_Template_ion_button_click_17_listener"], "sources": ["C:\\Users\\<USER>\\Downloads\\Work __<PERSON><PERSON><PERSON><PERSON><PERSON>_ouhna\\OCR_DOCUMENT_GROSSISTE\\Frontend ocr grossiste document\\frontend_ocr_grossiste_document\\src\\app\\welcome\\welcome.page.ts", "C:\\Users\\<USER>\\Downloads\\Work __<PERSON><PERSON><PERSON><PERSON><PERSON>_ouhna\\OCR_DOCUMENT_GROSSISTE\\Frontend ocr grossiste document\\frontend_ocr_grossiste_document\\src\\app\\welcome\\welcome.page.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { NavController } from '@ionic/angular';\r\n\r\n@Component({\r\n  selector: 'app-welcome',\r\n  templateUrl: './welcome.page.html',\r\n  styleUrls: ['./welcome.page.scss'],\r\n})\r\nexport class WelcomePage implements OnInit {\r\n\r\n  constructor(private navCtrl: NavController) {}\r\n\r\n  ngOnInit() {\r\n    console.log(\"Welcome Page\");\r\n\r\n  }\r\n\r\n  selectPlatform(platform: 'winpluspharma' | 'pharmalien') {\r\n    // Store the selected platform in localStorage\r\n    localStorage.setItem('src_app', platform);\r\n    console.log('Platform selected:', platform);\r\n\r\n    // Navigate to login page\r\n    this.navCtrl.navigateRoot('/login');\r\n  }\r\n}\r\n", "<ion-header>\r\n\r\n</ion-header>\r\n\r\n<ion-content >\r\n  <div class=\"welcome-wrapper\">\r\n        <ion-row>\r\n          <ion-col size=\"12\" class=\"slide-content\">\r\n            <img src=\"/assets/icon-welcome.svg\" class=\"slide-image\" />\r\n            <div class=\"content-slide\">\r\n              <h2><PERSON><PERSON><PERSON> ,<PERSON><PERSON><PERSON><PERSON><PERSON>, automatise<PERSON> !</h2>\r\n              <p>Vos bons de livraison enregistrés automatiquement en un instant.</p>\r\n            </div>\r\n          </ion-col>\r\n        </ion-row>\r\n\r\n\r\n  </div>\r\n</ion-content>\r\n    <ion-footer>\r\n        <h2>Choisissez votre plateforme</h2>\r\n        <div class=\"platform-selection\">\r\n          <ion-button\r\n            fill=\"clear\"\r\n            class=\"platform-button winplus-button\"\r\n            (click)=\"selectPlatform('winpluspharma')\">\r\n            <img src=\"/assets/onboarding_images/winpluspharm.svg\" alt=\"WinPlusPharm\" class=\"platform-logo\" />\r\n          </ion-button>\r\n          <ion-button\r\n            fill=\"clear\"\r\n            class=\"platform-button pharmalien-button\"\r\n            (click)=\"selectPlatform('pharmalien')\">\r\n            <img src=\"/assets/onboarding_images/winpharm.svg\" alt=\"Pharmalien\" class=\"platform-logo\" />\r\n          </ion-button>\r\n        </div>\r\n    </ion-footer>"], "mappings": ";;;AAQA,OAAM,MAAOA,WAAW;EAEtBC,YAAoBC,OAAsB;IAAtB,KAAAA,OAAO,GAAPA,OAAO;EAAkB;EAE7CC,QAAQA,CAAA;IACNC,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;EAE7B;EAEAC,cAAcA,CAACC,QAAwC;IACrD;IACAC,YAAY,CAACC,OAAO,CAAC,SAAS,EAAEF,QAAQ,CAAC;IACzCH,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEE,QAAQ,CAAC;IAE3C;IACA,IAAI,CAACL,OAAO,CAACQ,YAAY,CAAC,QAAQ,CAAC;EACrC;;eAhBWV,WAAW;;mBAAXA,YAAW,EAAAW,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,aAAA;AAAA;;QAAXd,YAAW;EAAAe,SAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,qBAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MCRxBV,EAAA,CAAAY,SAAA,iBAEa;MAKHZ,EAHV,CAAAa,cAAA,kBAAc,aACiB,cACd,iBACkC;MACvCb,EAAA,CAAAY,SAAA,aAA0D;MAExDZ,EADF,CAAAa,cAAA,aAA2B,SACrB;MAAAb,EAAA,CAAAc,MAAA,kDAAiC;MAAAd,EAAA,CAAAe,YAAA,EAAK;MAC1Cf,EAAA,CAAAa,cAAA,QAAG;MAAAb,EAAA,CAAAc,MAAA,6EAAgE;MAOjFd,EAPiF,CAAAe,YAAA,EAAI,EACnE,EACE,EACF,EAGV,EACM;MAENf,EADJ,CAAAa,cAAA,kBAAY,UACJ;MAAAb,EAAA,CAAAc,MAAA,mCAA2B;MAAAd,EAAA,CAAAe,YAAA,EAAK;MAElCf,EADF,CAAAa,cAAA,cAAgC,qBAIc;MAA1Cb,EAAA,CAAAgB,UAAA,mBAAAC,kDAAA;QAAA,OAASN,GAAA,CAAAhB,cAAA,CAAe,eAAe,CAAC;MAAA,EAAC;MACzCK,EAAA,CAAAY,SAAA,cAAiG;MACnGZ,EAAA,CAAAe,YAAA,EAAa;MACbf,EAAA,CAAAa,cAAA,qBAGyC;MAAvCb,EAAA,CAAAgB,UAAA,mBAAAE,kDAAA;QAAA,OAASP,GAAA,CAAAhB,cAAA,CAAe,YAAY,CAAC;MAAA,EAAC;MACtCK,EAAA,CAAAY,SAAA,cAA2F;MAGnGZ,EAFM,CAAAe,YAAA,EAAa,EACT,EACG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}