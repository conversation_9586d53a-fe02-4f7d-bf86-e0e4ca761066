{"ast": null, "code": "var _AuthGuard;\nimport { jwtDecode } from 'jwt-decode';\nimport * as moment from 'moment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class AuthGuard {\n  constructor(router) {\n    this.router = router;\n  }\n  canActivate() {\n    const tokenUser = localStorage.getItem('tokenUser');\n    const tokenTenant = localStorage.getItem('tokenTenant');\n    const tokenLocal = localStorage.getItem('token');\n    const platform = localStorage.getItem('src_app');\n    // Check if platform is selected\n    if (!platform) {\n      this.router.navigate(['/welcome']);\n      return false;\n    }\n    if (platform === 'pharmalien') {\n      // Pharmalien platform: only needs tokenUser and tokenLocal\n      if (tokenUser && tokenLocal) {\n        try {\n          const isTokenUserValid = this.checkTokenExpiration(tokenUser);\n          const isTokenLocalValid = this.checkTokenExpiration(tokenLocal);\n          if (isTokenUserValid && isTokenLocalValid) {\n            return true;\n          }\n        } catch (error) {\n          console.error('Token validation error:', error);\n        }\n      }\n    } else if (platform === 'winpluspharma') {\n      // WinPlus platform: needs all three tokens\n      if (tokenUser && tokenTenant && tokenLocal) {\n        try {\n          const isTokenUserValid = this.checkTokenExpiration(tokenUser);\n          const isTokenTenantValid = this.checkTokenExpiration(tokenTenant);\n          const isTokenLocalValid = this.checkTokenExpiration(tokenLocal);\n          if (isTokenUserValid && isTokenTenantValid && isTokenLocalValid) {\n            return true;\n          }\n        } catch (error) {\n          console.error('Token validation error:', error);\n        }\n      }\n    }\n    // If any required token is missing or expired, redirect to login\n    this.router.navigate(['/login']);\n    return false;\n  }\n  checkTokenExpiration(token) {\n    const decodedToken = jwtDecode(token);\n    const expiration = moment((decodedToken === null || decodedToken === void 0 ? void 0 : decodedToken.exp) * 1000);\n    return moment(new Date()) < expiration;\n  }\n}\n_AuthGuard = AuthGuard;\n_AuthGuard.ɵfac = function AuthGuard_Factory(t) {\n  return new (t || _AuthGuard)(i0.ɵɵinject(i1.Router));\n};\n_AuthGuard.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n  token: _AuthGuard,\n  factory: _AuthGuard.ɵfac,\n  providedIn: 'root'\n});", "map": {"version": 3, "names": ["jwtDecode", "moment", "<PERSON><PERSON><PERSON><PERSON>", "constructor", "router", "canActivate", "tokenUser", "localStorage", "getItem", "tokenTenant", "tokenLocal", "platform", "navigate", "isTokenUserValid", "checkTokenExpiration", "isTokenLocalValid", "error", "console", "isTokenTenantValid", "token", "decodedToken", "expiration", "exp", "Date", "i0", "ɵɵinject", "i1", "Router", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Downloads\\Work __<PERSON><PERSON><PERSON><PERSON><PERSON>_ouhna\\OCR_DOCUMENT_GROSSISTE\\Frontend ocr grossiste document\\frontend_ocr_grossiste_document\\src\\app\\interceptors\\auth.guard.ts"], "sourcesContent": ["// auth.guard.ts\r\nimport { Injectable } from '@angular/core';\r\nimport { CanActivate, Router } from '@angular/router';\r\nimport { jwtDecode } from 'jwt-decode';\r\nimport * as moment from 'moment';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class AuthGuard implements CanActivate {\r\n\r\n  constructor(private router: Router) {}\r\n\r\n  canActivate(): boolean {\r\n    const tokenUser = localStorage.getItem('tokenUser');\r\n    const tokenTenant = localStorage.getItem('tokenTenant');\r\n    const tokenLocal = localStorage.getItem('token');\r\n    const platform = localStorage.getItem('src_app');\r\n\r\n    // Check if platform is selected\r\n    if (!platform) {\r\n      this.router.navigate(['/welcome']);\r\n      return false;\r\n    }\r\n\r\n    if (platform === 'pharmalien') {\r\n      // Pharmalien platform: only needs tokenUser and tokenLocal\r\n      if (tokenUser && tokenLocal) {\r\n        try {\r\n          const isTokenUserValid = this.checkTokenExpiration(tokenUser);\r\n          const isTokenLocalValid = this.checkTokenExpiration(tokenLocal);\r\n\r\n          if (isTokenUserValid && isTokenLocalValid) {\r\n            return true;\r\n          }\r\n        } catch (error) {\r\n          console.error('Token validation error:', error);\r\n        }\r\n      }\r\n    } else if (platform === 'winpluspharma') {\r\n      // WinPlus platform: needs all three tokens\r\n      if (tokenUser && tokenTenant && tokenLocal) {\r\n        try {\r\n          const isTokenUserValid = this.checkTokenExpiration(tokenUser);\r\n          const isTokenTenantValid = this.checkTokenExpiration(tokenTenant);\r\n          const isTokenLocalValid = this.checkTokenExpiration(tokenLocal);\r\n\r\n          if (isTokenUserValid && isTokenTenantValid && isTokenLocalValid) {\r\n            return true;\r\n          }\r\n        } catch (error) {\r\n          console.error('Token validation error:', error);\r\n        }\r\n      }\r\n    }\r\n\r\n    // If any required token is missing or expired, redirect to login\r\n    this.router.navigate(['/login']);\r\n    return false;\r\n  }\r\n\r\n  private checkTokenExpiration(token: string): boolean {\r\n    const decodedToken: any = jwtDecode(token);\r\n    const expiration = moment(decodedToken?.exp * 1000);\r\n    return moment(new Date()) < expiration;\r\n  }\r\n}"], "mappings": ";AAGA,SAASA,SAAS,QAAQ,YAAY;AACtC,OAAO,KAAKC,MAAM,MAAM,QAAQ;;;AAKhC,OAAM,MAAOC,SAAS;EAEpBC,YAAoBC,MAAc;IAAd,KAAAA,MAAM,GAANA,MAAM;EAAW;EAErCC,WAAWA,CAAA;IACT,MAAMC,SAAS,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;IACnD,MAAMC,WAAW,GAAGF,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;IACvD,MAAME,UAAU,GAAGH,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAChD,MAAMG,QAAQ,GAAGJ,YAAY,CAACC,OAAO,CAAC,SAAS,CAAC;IAEhD;IACA,IAAI,CAACG,QAAQ,EAAE;MACb,IAAI,CAACP,MAAM,CAACQ,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC;MAClC,OAAO,KAAK;;IAGd,IAAID,QAAQ,KAAK,YAAY,EAAE;MAC7B;MACA,IAAIL,SAAS,IAAII,UAAU,EAAE;QAC3B,IAAI;UACF,MAAMG,gBAAgB,GAAG,IAAI,CAACC,oBAAoB,CAACR,SAAS,CAAC;UAC7D,MAAMS,iBAAiB,GAAG,IAAI,CAACD,oBAAoB,CAACJ,UAAU,CAAC;UAE/D,IAAIG,gBAAgB,IAAIE,iBAAiB,EAAE;YACzC,OAAO,IAAI;;SAEd,CAAC,OAAOC,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;;;KAGpD,MAAM,IAAIL,QAAQ,KAAK,eAAe,EAAE;MACvC;MACA,IAAIL,SAAS,IAAIG,WAAW,IAAIC,UAAU,EAAE;QAC1C,IAAI;UACF,MAAMG,gBAAgB,GAAG,IAAI,CAACC,oBAAoB,CAACR,SAAS,CAAC;UAC7D,MAAMY,kBAAkB,GAAG,IAAI,CAACJ,oBAAoB,CAACL,WAAW,CAAC;UACjE,MAAMM,iBAAiB,GAAG,IAAI,CAACD,oBAAoB,CAACJ,UAAU,CAAC;UAE/D,IAAIG,gBAAgB,IAAIK,kBAAkB,IAAIH,iBAAiB,EAAE;YAC/D,OAAO,IAAI;;SAEd,CAAC,OAAOC,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;;;;IAKrD;IACA,IAAI,CAACZ,MAAM,CAACQ,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;IAChC,OAAO,KAAK;EACd;EAEQE,oBAAoBA,CAACK,KAAa;IACxC,MAAMC,YAAY,GAAQpB,SAAS,CAACmB,KAAK,CAAC;IAC1C,MAAME,UAAU,GAAGpB,MAAM,CAAC,CAAAmB,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEE,GAAG,IAAG,IAAI,CAAC;IACnD,OAAOrB,MAAM,CAAC,IAAIsB,IAAI,EAAE,CAAC,GAAGF,UAAU;EACxC;;aAxDWnB,SAAS;;mBAATA,UAAS,EAAAsB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,MAAA;AAAA;;SAATzB,UAAS;EAAA0B,OAAA,EAAT1B,UAAS,CAAA2B,IAAA;EAAAC,UAAA,EAFR;AAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}