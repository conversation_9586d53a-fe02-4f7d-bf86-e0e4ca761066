{"ast": null, "code": "var _WelcomePage;\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ionic/angular\";\nexport class WelcomePage {\n  constructor(navCtrl) {\n    this.navCtrl = navCtrl;\n  }\n  ngOnInit() {\n    console.log(\"Welcome Page\");\n  }\n  selectPlatform(platform) {\n    // Store the selected platform\n    localStorage.setItem('src_app', platform);\n    // Navigate to login with platform parameter\n    this.navCtrl.navigateForward('/login', {\n      queryParams: {\n        platform: platform\n      }\n    });\n  }\n}\n_WelcomePage = WelcomePage;\n_WelcomePage.ɵfac = function WelcomePage_Factory(t) {\n  return new (t || _WelcomePage)(i0.ɵɵdirectiveInject(i1.NavController));\n};\n_WelcomePage.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: _WelcomePage,\n  selectors: [[\"app-welcome\"]],\n  decls: 18,\n  vars: 0,\n  consts: [[1, \"welcome-wrapper\"], [1, \"main-content\"], [\"src\", \"/assets/icon-welcome.svg\", 1, \"welcome-icon\"], [1, \"welcome-text\"], [1, \"platform-section\"], [1, \"platform-buttons\"], [1, \"platform-button\", 3, \"click\"], [\"src\", \"assets/onboarding_images/winpluspharm.svg\", \"alt\", \"WinPlus Pharma\", 1, \"platform-image\"], [\"src\", \"assets/onboarding_images/winpharm.svg\", \"alt\", \"Pharmalier\", 1, \"platform-image\"]],\n  template: function WelcomePage_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelement(0, \"ion-header\");\n      i0.ɵɵelementStart(1, \"ion-content\")(2, \"div\", 0)(3, \"div\", 1);\n      i0.ɵɵelement(4, \"img\", 2);\n      i0.ɵɵelementStart(5, \"div\", 3)(6, \"h2\");\n      i0.ɵɵtext(7, \"Scannez, r\\u00E9cup\\u00E9rez, automatisez !\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(8, \"p\");\n      i0.ɵɵtext(9, \"Vos bons de livraison enregistr\\u00E9s automatiquement en un instant.\");\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(10, \"div\", 4)(11, \"h3\");\n      i0.ɵɵtext(12, \"Choisir votre plateforme\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(13, \"div\", 5)(14, \"div\", 6);\n      i0.ɵɵlistener(\"click\", function WelcomePage_Template_div_click_14_listener() {\n        return ctx.selectPlatform(\"winpluspharma\");\n      });\n      i0.ɵɵelement(15, \"img\", 7);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(16, \"div\", 6);\n      i0.ɵɵlistener(\"click\", function WelcomePage_Template_div_click_16_listener() {\n        return ctx.selectPlatform(\"pharmalier\");\n      });\n      i0.ɵɵelement(17, \"img\", 8);\n      i0.ɵɵelementEnd()()()()();\n    }\n  },\n  dependencies: [i1.IonContent, i1.IonHeader],\n  styles: [\"@import url(https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap);@import url(https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0[_ngcontent-%COMP%], 200[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 300[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 400[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 500[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 600[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 700[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 800[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 900[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 100[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 200[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 300[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 400[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 500[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 600[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 700[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 800[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 900&display=swap)[_ngcontent-%COMP%];*[_ngcontent-%COMP%] {\\n  font-family: \\\"Inter\\\", sans-serif;\\n  font-optical-sizing: auto;\\n}\\n\\nion-content[_ngcontent-%COMP%]::part(scroll) {\\n  overflow-y: auto;\\n}\\n\\nion-button[_ngcontent-%COMP%]::part(native) {\\n  --padding-top: 20px !important;\\n  --padding-bottom: 20px !important;\\n}\\n\\n.welcome-wrapper[_ngcontent-%COMP%] {\\n  background: url(\\\"/assets/bg-welcome.png\\\") no-repeat center center fixed;\\n  background-size: cover;\\n  height: 100%;\\n}\\n\\nion-footer[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n  padding: 10px;\\n  display: flex;\\n  justify-content: center;\\n  flex-direction: row;\\n  align-items: center;\\n}\\n\\nion-toolbar[_ngcontent-%COMP%] {\\n  --background: transparent;\\n  --ion-color-primary: #2f4fcd;\\n}\\n\\nion-button[_ngcontent-%COMP%] {\\n  margin: 10px;\\n}\\n\\nion-button.welcome-button[_ngcontent-%COMP%] {\\n  --background: #1f41bb;\\n  --background-activated: #1f41bb;\\n  --border-radius: 8px;\\n  width: 65%;\\n  text-align: center;\\n  box-shadow: 0px 16px 20px rgb(203, 214, 255); \\n\\n  color: #fff;\\n  font-size: 20px;\\n  font-weight: bold;\\n}\\n\\nion-row[_ngcontent-%COMP%] {\\n  height: 100%;\\n  height: 85vh;\\n}\\n\\n  ion-row ion-col {\\n  padding-bottom: 0 !important;\\n}\\n\\n  ion-col {\\n  display: flex !important;\\n  flex-direction: column;\\n  justify-content: space-evenly;\\n  align-items: center;\\n}\\n\\n  img {\\n  width: auto;\\n  max-width: 100%;\\n  height: auto;\\n  max-height: 100%;\\n}\\n\\n  .content-slide {\\n  text-align: left;\\n  padding: 0 20px;\\n}\\n\\n  .content-slide h2 {\\n  font-family: \\\"Poppins\\\", \\\"Inter\\\", sans-serif !important;\\n  font-weight: bold;\\n  font-style: normal;\\n  font-size: 30px;\\n  color: #1f41bb;\\n}\\n\\n  .content-slide p {\\n  padding-right: 20px;\\n  margin-top: 40px;\\n  letter-spacing: 1.1px;\\n}\\n\\n.welcome-wrapper[_ngcontent-%COMP%] {\\n  height: 100vh;\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: center;\\n  align-items: center;\\n  padding: 20px;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n}\\n\\n.main-content[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 40px;\\n  color: white;\\n}\\n.main-content[_ngcontent-%COMP%]   .welcome-icon[_ngcontent-%COMP%] {\\n  width: 80px;\\n  height: 80px;\\n  margin-bottom: 20px;\\n}\\n.main-content[_ngcontent-%COMP%]   .welcome-text[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-family: \\\"Poppins\\\", \\\"Inter\\\", sans-serif !important;\\n  font-weight: 700;\\n  font-size: 1.8rem;\\n  margin-bottom: 10px;\\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\\n}\\n.main-content[_ngcontent-%COMP%]   .welcome-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  opacity: 0.9;\\n  margin: 0;\\n  line-height: 1.4;\\n}\\n\\n.platform-section[_ngcontent-%COMP%] {\\n  text-align: center;\\n  width: 100%;\\n  max-width: 300px;\\n}\\n.platform-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-family: \\\"Poppins\\\", \\\"Inter\\\", sans-serif !important;\\n  font-weight: 600;\\n  font-size: 1.2rem;\\n  color: white;\\n  margin-bottom: 20px;\\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\\n}\\n\\n.platform-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  gap: 20px;\\n}\\n\\n.platform-button[_ngcontent-%COMP%] {\\n  flex: 1;\\n  background: white;\\n  border-radius: 16px;\\n  padding: 20px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\\n  border: 2px solid transparent;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n}\\n.platform-button[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);\\n  border-color: #667eea;\\n}\\n.platform-button[_ngcontent-%COMP%]:active {\\n  transform: translateY(0);\\n}\\n.platform-button[_ngcontent-%COMP%]   .platform-image[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  object-fit: contain;\\n}\\n\\n@media (max-width: 768px) {\\n  .main-content[_ngcontent-%COMP%] {\\n    margin-bottom: 30px;\\n  }\\n  .main-content[_ngcontent-%COMP%]   .welcome-icon[_ngcontent-%COMP%] {\\n    width: 70px;\\n    height: 70px;\\n  }\\n  .main-content[_ngcontent-%COMP%]   .welcome-text[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n    font-size: 1.6rem;\\n  }\\n  .main-content[_ngcontent-%COMP%]   .welcome-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n    font-size: 0.9rem;\\n  }\\n  .platform-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n    font-size: 1.1rem;\\n  }\\n  .platform-button[_ngcontent-%COMP%] {\\n    padding: 15px;\\n  }\\n  .platform-button[_ngcontent-%COMP%]   .platform-image[_ngcontent-%COMP%] {\\n    width: 50px;\\n    height: 50px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .welcome-wrapper[_ngcontent-%COMP%] {\\n    padding: 15px;\\n  }\\n  .main-content[_ngcontent-%COMP%] {\\n    margin-bottom: 25px;\\n  }\\n  .main-content[_ngcontent-%COMP%]   .welcome-icon[_ngcontent-%COMP%] {\\n    width: 60px;\\n    height: 60px;\\n  }\\n  .main-content[_ngcontent-%COMP%]   .welcome-text[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n    font-size: 1.4rem;\\n  }\\n  .main-content[_ngcontent-%COMP%]   .welcome-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n    font-size: 0.85rem;\\n  }\\n  .platform-buttons[_ngcontent-%COMP%] {\\n    gap: 15px;\\n  }\\n  .platform-button[_ngcontent-%COMP%] {\\n    padding: 12px;\\n  }\\n  .platform-button[_ngcontent-%COMP%]   .platform-image[_ngcontent-%COMP%] {\\n    width: 45px;\\n    height: 45px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n});", "map": {"version": 3, "names": ["WelcomePage", "constructor", "navCtrl", "ngOnInit", "console", "log", "selectPlatform", "platform", "localStorage", "setItem", "navigateForward", "queryParams", "i0", "ɵɵdirectiveInject", "i1", "NavController", "selectors", "decls", "vars", "consts", "template", "WelcomePage_Template", "rf", "ctx", "ɵɵelement", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "WelcomePage_Template_div_click_14_listener", "WelcomePage_Template_div_click_16_listener"], "sources": ["C:\\Users\\<USER>\\Downloads\\Work __<PERSON><PERSON><PERSON><PERSON><PERSON>_ouhna\\OCR_DOCUMENT_GROSSISTE\\Frontend ocr grossiste document\\frontend_ocr_grossiste_document\\src\\app\\welcome\\welcome.page.ts", "C:\\Users\\<USER>\\Downloads\\Work __<PERSON><PERSON><PERSON><PERSON><PERSON>_ouhna\\OCR_DOCUMENT_GROSSISTE\\Frontend ocr grossiste document\\frontend_ocr_grossiste_document\\src\\app\\welcome\\welcome.page.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { NavController } from '@ionic/angular';\r\n\r\n@Component({\r\n  selector: 'app-welcome',\r\n  templateUrl: './welcome.page.html',\r\n  styleUrls: ['./welcome.page.scss'],\r\n})\r\nexport class WelcomePage implements OnInit {\r\n\r\n  constructor(private navCtrl: NavController) {}\r\n\r\n  ngOnInit() {\r\n    console.log(\"Welcome Page\");\r\n  }\r\n\r\n  selectPlatform(platform: 'winpluspharma' | 'pharmalier') {\r\n    // Store the selected platform\r\n    localStorage.setItem('src_app', platform);\r\n\r\n    // Navigate to login with platform parameter\r\n    this.navCtrl.navigateForward('/login', {\r\n      queryParams: { platform: platform }\r\n    });\r\n  }\r\n}\r\n", "<ion-header>\r\n\r\n</ion-header>\r\n\r\n<ion-content>\r\n  <div class=\"welcome-wrapper\">\r\n    <!-- Main Content -->\r\n    <div class=\"main-content\">\r\n      <img src=\"/assets/icon-welcome.svg\" class=\"welcome-icon\" />\r\n      <div class=\"welcome-text\">\r\n        <h2><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, automatisez !</h2>\r\n        <p>Vos bons de livraison enregistrés automatiquement en un instant.</p>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Platform Selection -->\r\n    <div class=\"platform-section\">\r\n      <h3>Choisir votre plateforme</h3>\r\n\r\n      <div class=\"platform-buttons\">\r\n        <div class=\"platform-button\" (click)=\"selectPlatform('winpluspharma')\">\r\n          <img src=\"assets/onboarding_images/winpluspharm.svg\" alt=\"WinPlus Pharma\" class=\"platform-image\">\r\n        </div>\r\n\r\n        <div class=\"platform-button\" (click)=\"selectPlatform('pharmalier')\">\r\n          <img src=\"assets/onboarding_images/winpharm.svg\" alt=\"Pharmalier\" class=\"platform-image\">\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</ion-content>"], "mappings": ";;;AAQA,OAAM,MAAOA,WAAW;EAEtBC,YAAoBC,OAAsB;IAAtB,KAAAA,OAAO,GAAPA,OAAO;EAAkB;EAE7CC,QAAQA,CAAA;IACNC,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;EAC7B;EAEAC,cAAcA,CAACC,QAAwC;IACrD;IACAC,YAAY,CAACC,OAAO,CAAC,SAAS,EAAEF,QAAQ,CAAC;IAEzC;IACA,IAAI,CAACL,OAAO,CAACQ,eAAe,CAAC,QAAQ,EAAE;MACrCC,WAAW,EAAE;QAAEJ,QAAQ,EAAEA;MAAQ;KAClC,CAAC;EACJ;;eAhBWP,WAAW;;mBAAXA,YAAW,EAAAY,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,aAAA;AAAA;;QAAXf,YAAW;EAAAgB,SAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,qBAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MCRxBV,EAAA,CAAAY,SAAA,iBAEa;MAKTZ,EAHJ,CAAAa,cAAA,kBAAa,aACkB,aAED;MACxBb,EAAA,CAAAY,SAAA,aAA2D;MAEzDZ,EADF,CAAAa,cAAA,aAA0B,SACpB;MAAAb,EAAA,CAAAc,MAAA,kDAAiC;MAAAd,EAAA,CAAAe,YAAA,EAAK;MAC1Cf,EAAA,CAAAa,cAAA,QAAG;MAAAb,EAAA,CAAAc,MAAA,4EAAgE;MAEvEd,EAFuE,CAAAe,YAAA,EAAI,EACnE,EACF;MAIJf,EADF,CAAAa,cAAA,cAA8B,UACxB;MAAAb,EAAA,CAAAc,MAAA,gCAAwB;MAAAd,EAAA,CAAAe,YAAA,EAAK;MAG/Bf,EADF,CAAAa,cAAA,cAA8B,cAC2C;MAA1Cb,EAAA,CAAAgB,UAAA,mBAAAC,2CAAA;QAAA,OAASN,GAAA,CAAAjB,cAAA,CAAe,eAAe,CAAC;MAAA,EAAC;MACpEM,EAAA,CAAAY,SAAA,cAAiG;MACnGZ,EAAA,CAAAe,YAAA,EAAM;MAENf,EAAA,CAAAa,cAAA,cAAoE;MAAvCb,EAAA,CAAAgB,UAAA,mBAAAE,2CAAA;QAAA,OAASP,GAAA,CAAAjB,cAAA,CAAe,YAAY,CAAC;MAAA,EAAC;MACjEM,EAAA,CAAAY,SAAA,cAAyF;MAKnGZ,EAJQ,CAAAe,YAAA,EAAM,EACF,EACF,EACF,EACM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}