from enum import Enum
from pydantic import BaseModel
from typing import List, Optional, Any
from datetime import date


class ModelName(str, Enum):
    global_model = 'GLOBAL'
    sophaca = 'SOPHACA'
    ugp = 'UGP'
    dipharm = 'DIPHARM'
    giphar = 'GIPHAR'
    spr = 'SPR'
    sophadims = 'SOPHADIMS'
    gpm = 'GPM'
    recamed = 'RECAMED'
    cooper_pharma_casa = 'COOPER_PHARMA_CASA'
    sophagharb = 'SOPHAGHARB'
    cpre = 'CPRE'
    sophafas = 'SOPHAFAS'
    rephak = 'REPHAK'
    sophasais = 'SOPHASAIS'


class PreBlOcrResponse(BaseModel):
    ID_BL: int
    Content: Any  # Use Any to allow flexibility in the Content structure
    ID_USER: str
    status: str
    ID_TENANT: str
    date: str
    id_BL_origine: str
    date_BL_origine: str
    supplier_name: str
    supplier_id: str
    random_id: str
    src_app: str = 'winpluspharma'  # Platform source with default value


class UpdateStatusRequest(BaseModel):
    status: str
    id_BL_origine: str
    date_BL_origine: str
    supplier_name: str


class RequestCriteria(BaseModel):
    """
    Dynamic filtering criteria for BL scanning endpoints.
    All fields are optional to support flexible filtering.
    """
    NumeroBL: Optional[str] = None  # Maps to ID_BL in pre_bl_ocr table
    dateBl: Optional[str] = None    # Maps to date_BL_origine in pre_bl_ocr table
    dateBlDebut: Optional[str] = None  # Start date for date range filtering
    dateBlFin: Optional[str] = None    # End date for date range filtering


class ImageData(BaseModel):
    image: str
    model_name: str
    random_id: Optional[str] = None
    job_id: Optional[str] = None

class ProcessOcrMultiRequest(BaseModel):
    images: List[ImageData]
    ocr_mode: str = "standard"
    src_app: str = "winpluspharma"  # Platform source with default value

class Coordinate(BaseModel):
    x: float
    y: float


class ProcessImageSuppRequest(BaseModel):
    coordinates: List[Coordinate]
    random_id: str
    model_name: ModelName
    job_id: str
    rotation: int


class TenantLoginRequest(BaseModel):
    username: str
    password: str


class TenantLoginResponse(BaseModel):
    accessToken: str
    message: str


class LoginRequest(BaseModel):
    username: str
    password: str
    tenant_token: str


class LoginResponse(BaseModel):
    tenant_data: dict
    user_data: dict
    local_token: str
    message: str


class PharmalienLoginRequest(BaseModel):
    username: str
    password: str


class PharmalienLoginResponse(BaseModel):
    user_data: dict
    local_token: str
    message: str


class JSONDataRequest(BaseModel):
    data: dict
