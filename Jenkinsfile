pipeline {
    agent any
    parameters {
        string(name: 'IMAGE_VERSION', defaultValue: '1', description: 'Version for the new Docker image')
        string(name: 'IMAGE_NAME', defaultValue: 'windoc_base', description: 'Name of the new Docker image')
    }
    environment {
       WINDOC_IMAGE_NAME = 'windoc-api'
       CONTAINER_NAME = "${WINDOC_IMAGE_NAME}.prod"
    }

    stages {

        // delete old container after stopping it
        stage('stop and delete container of Windoc API') {
            steps {
                script {
                    sh "docker stop ${WINDOC_IMAGE_NAME} || true"
                    sh "docker rm ${WINDOC_IMAGE_NAME} || true"
                }
            }
        }


        stage('Clean Up Old Images of Windoc API') {
            steps {
                script {
                    sh """
                        docker images -q '${WINDOC_IMAGE_NAME}:v*' | xargs -r docker rmi -f
                    """
                }
            }
        }
        stage('Build New Image for Windoc API') {
            steps {
                script {
                    // Build the new Docker image
                    sh "docker build -t ${WINDOC_IMAGE_NAME}:v${IMAGE_VERSION} . --build-arg VERSION=${IMAGE_VERSION}"
                }
            }
        }


        stage('Start Windoc API Container') {
                steps {
                    script {
                        sh """
                            docker run -d  --network=backend.prod --name ${CONTAINER_NAME} ${WINDOC_IMAGE_NAME}:v${IMAGE_VERSION} 
                        """
                    }
                }
        }
        }
}