from fastapi import Header, HTTPException, Depends
from src.app.utils.jwt_utils import verify_token, verify_token_winplus


def check_headers(
        Authorization: str = Head<PERSON>(...),
        AuthorizationTenant: str = Header(...),
        AuthorizationUser: str = Header(...)
):
    if not Authorization or not AuthorizationTenant or not AuthorizationUser:
        raise HTTPException(status_code=401, detail="Missing authorization headers")
    return {
        "Authorization": Authorization,
        "AuthorizationTenant": AuthorizationTenant,
        "AuthorizationUser": AuthorizationUser
    }


def check_tenant_user_headers_winplus(
        Authorization: str = Header(...),
        AuthorizationTenant: str = Header(...)
):
    if not Authorization or not AuthorizationTenant:
        raise HTTPException(status_code=401, detail="Missing AuthorizationTenant or Authorization headers")

    try:
        # Verify user token
        user_token = Authorization.replace("Bearer ", "")
        user_payload = verify_token_winplus(user_token)

        # Verify tenant token
        tenant_token = AuthorizationTenant.replace("BearerTenant ", "")
        tenant_payload = verify_token_winplus(tenant_token, is_tenant=True)

        return {
            "user": user_payload,
            "tenant": tenant_payload
        }
    except Exception as e:
        raise HTTPException(status_code=401, detail=f"Invalid token: {str(e)}")


# def check_tenant_user_headers_winplus(
#         Authorization: str = Header(...),
#         AuthorizationTenant: str = Header(...)
# ):
#     if not Authorization or not AuthorizationTenant:
#         raise HTTPException(status_code=401, detail="Missing AuthorizationTenant or Authorization headers")
#     return {
#         "AuthorizationTenant": AuthorizationTenant,
#         "Authorization": Authorization
#     }


def get_current_user(Authorization: str = Header(...)):
    try:
        token = Authorization.replace("Bearer ", "")
        return verify_token(token)
    except HTTPException as e:
        raise HTTPException(status_code=e.status_code, detail="Invalid token")


def check_pharmalien_headers(Authorization: str = Header(...)):
    """Check headers for Pharmalien authentication (single token)"""
    if not Authorization:
        raise HTTPException(status_code=401, detail="Missing authorization header")
    return {"Authorization": Authorization}


def check_windoc_headers(
    Authorization: str = Header(...),
    IdHashUser: str = Header(...)
):
    """
    Check headers for Windoc authentication (static token + IdHashUser)
    Expected format:
    - Authorization: kcS5RMvQ4JRuVcqEwyFdbJbpbPOk5ZSBAm4CsrqeX106f0Pi8Lt4qBL0Uitk2TtqPJtmqFwWa1iX5abIjwj3ppHeXymxgkWr0Gy6jIcIFQmYWZxywfQqllvV
    - IdHashUser: <user_hash_id>
    """
    if not Authorization:
        raise HTTPException(status_code=401, detail="Missing Authorization header")

    if not IdHashUser:
        raise HTTPException(status_code=401, detail="Missing IdHashUser header")

    # Define the expected static token
    EXPECTED_STATIC_TOKEN = "kcS5RMvQ4JRuVcqEwyFdbJbpbPOk5ZSBAm4CsrqeX106f0Pi8Lt4qBL0Uitk2TtqPJtmqFwWa1iX5abIjwj3ppHeXymxgkWr0Gy6jIcIFQmYWZxywfQqllvV"

    # Validate static token
    if Authorization.strip() != EXPECTED_STATIC_TOKEN:
        raise HTTPException(status_code=401, detail="Invalid static token")

    # Basic validation for IdHashUser (ensure it's not empty)
    if not IdHashUser.strip():
        raise HTTPException(status_code=401, detail="IdHashUser cannot be empty")

    return {
        "Authorization": Authorization,
        "IdHashUser": IdHashUser
    }
