# Prod environment variables
# Application Environment
ENVIRONMENT=prod
DEBUG=True
SYS_ARGV=api

# Tesseract OCR Configuration
TESSERACT_PATH=/usr/bin/tesseract

# API Configuration
API_URL=https://winproduit.sophatel.com:8000
TAP_URL=https://winproduit.sophatel.com:8005

# WinPlus ERP Integration
WINPLUS_AUTH_USER=https://winpharmplus.ma/api/user/auth
WINPLUS_AUTH_TENANT=https://winpharmplus.ma/api/user/auth-tenant
WINPLUS_URL=https://winpharmplus.ma

# Pharmalien Integration
PHARMALIEN_AUTH_URL=https://pharmalien.ma/api/user/auth

# JWT Authentication
SECRET_KEY=paython-ocr-insecure-#8!7z!_
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=180 # 3 hours

# PostgreSQL Database Configuration (VPS)
POSTGRES_HOST=vps6.sophatel.com
POSTGRES_PORT=5432
POSTGRES_DB=ocr_document_grossiste
POSTGRES_USER=postgres 
POSTGRES_PASSWORD=JX4yxIN7fBiDDonrf3tSXhVxoS7B 
POSTGRES_SSL_MODE=prefer
