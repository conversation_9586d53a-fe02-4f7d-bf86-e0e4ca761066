#!/usr/bin/env python3
"""
Test database connection script
This script tests the PostgreSQL database connection with detailed debugging.
"""

import sys
import os
import argparse
from pathlib import Path
from dotenv import load_dotenv
import logging

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_basic_connection():
    """Test basic PostgreSQL connection using psycopg2."""
    try:
        import psycopg2
        
        # Get connection parameters from environment
        host = os.getenv('POSTGRES_HOST')
        port = os.getenv('POSTGRES_PORT')
        database = os.getenv('POSTGRES_DB')
        user = os.getenv('POSTGRES_USER')
        password = os.getenv('POSTGRES_PASSWORD')
        
        logger.info(f"Testing connection to: {user}@{host}:{port}/{database}")
        
        # Test connection
        conn = psycopg2.connect(
            host=host,
            port=port,
            database=database,
            user=user,
            password=password
        )
        
        # Test query
        cursor = conn.cursor()
        cursor.execute("SELECT version();")
        version = cursor.fetchone()
        
        logger.info(f"✅ PostgreSQL connection successful!")
        logger.info(f"✅ PostgreSQL version: {version[0]}")
        
        cursor.close()
        conn.close()
        return True
        
    except ImportError:
        logger.error("❌ psycopg2 not installed. Run: pip install psycopg2-binary")
        return False
    except Exception as e:
        logger.error(f"❌ PostgreSQL connection failed: {e}")
        return False

def test_sqlalchemy_connection():
    """Test SQLAlchemy connection."""
    try:
        from sqlalchemy import create_engine, text
        
        # Build connection URL
        host = os.getenv('POSTGRES_HOST')
        port = os.getenv('POSTGRES_PORT')
        database = os.getenv('POSTGRES_DB')
        user = os.getenv('POSTGRES_USER')
        password = os.getenv('POSTGRES_PASSWORD')
        
        url = f"postgresql://{user}:{password}@{host}:{port}/{database}"
        logger.info(f"SQLAlchemy URL: postgresql://{user}:***@{host}:{port}/{database}")
        
        engine = create_engine(url)
        
        # Test connection
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1"))
            logger.info("✅ SQLAlchemy connection successful!")
            return True
            
    except Exception as e:
        logger.error(f"❌ SQLAlchemy connection failed: {e}")
        return False

def test_app_db_manager():
    """Test the application's database manager."""
    try:
        from src.app.database.connection import db_manager
        
        logger.info("Testing application database manager...")
        
        # Check if db_manager is initialized
        if not hasattr(db_manager, 'postgres_engine') or db_manager.postgres_engine is None:
            logger.error("❌ Database manager not initialized")
            return False
        
        # Test health check
        health = db_manager.health_check()
        logger.info(f"Health check result: {health}")
        
        if health.get('postgresql', {}).get('available'):
            logger.info("✅ Application database manager working!")
            return True
        else:
            logger.error(f"❌ Database manager health check failed: {health}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Application database manager test failed: {e}")
        return False

def check_environment_vars():
    """Check if all required environment variables are set."""
    required_vars = [
        'POSTGRES_HOST',
        'POSTGRES_PORT', 
        'POSTGRES_DB',
        'POSTGRES_USER',
        'POSTGRES_PASSWORD'
    ]
    
    logger.info("Checking environment variables:")
    missing_vars = []
    for var in required_vars:
        value = os.getenv(var)
        if value:
            if var == 'POSTGRES_PASSWORD':
                logger.info(f"  {var}: ***")
            else:
                logger.info(f"  {var}: {value}")
        else:
            missing_vars.append(var)
            logger.error(f"  {var}: NOT SET")
    
    if missing_vars:
        logger.error(f"❌ Missing environment variables: {', '.join(missing_vars)}")
        return False
    
    logger.info("✅ All environment variables are set")
    return True

def main():
    """Main test function."""
    parser = argparse.ArgumentParser(description='Test database connection')
    parser.add_argument('--env-file', default='.env.local', help='Environment file to load (default: .env.local)')
    args = parser.parse_args()
    
    # Load environment file
    env_file_path = project_root / args.env_file
    if env_file_path.exists():
        load_dotenv(env_file_path)
        logger.info(f"✅ Loaded environment from {args.env_file}")
    else:
        logger.error(f"❌ Environment file not found: {args.env_file}")
        sys.exit(1)
    
    logger.info("🔍 Starting database connection tests...")
    
    # Test 1: Environment variables
    if not check_environment_vars():
        sys.exit(1)
    
    # Test 2: Basic psycopg2 connection
    logger.info("\n📡 Testing basic PostgreSQL connection...")
    if not test_basic_connection():
        sys.exit(1)
    
    # Test 3: SQLAlchemy connection
    logger.info("\n🔧 Testing SQLAlchemy connection...")
    if not test_sqlalchemy_connection():
        sys.exit(1)
    
    # Test 4: Application database manager
    logger.info("\n🏗️ Testing application database manager...")
    if not test_app_db_manager():
        sys.exit(1)
    
    logger.info("\n🎉 All database connection tests passed!")

if __name__ == "__main__":
    main()
