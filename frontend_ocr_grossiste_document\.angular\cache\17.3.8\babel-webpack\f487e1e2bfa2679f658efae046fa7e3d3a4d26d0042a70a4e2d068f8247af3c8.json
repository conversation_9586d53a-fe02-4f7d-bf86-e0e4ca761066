{"ast": null, "code": "var _WelcomePage;\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ionic/angular\";\nexport class WelcomePage {\n  constructor(navCtrl) {\n    this.navCtrl = navCtrl;\n  }\n  ngOnInit() {\n    console.log(\"Welcome Page\");\n  }\n  selectPlatform(platform) {\n    // Store the selected platform\n    localStorage.setItem('src_app', platform);\n    // Navigate to login with platform parameter\n    this.navCtrl.navigateForward('/login', {\n      queryParams: {\n        platform: platform\n      }\n    });\n  }\n}\n_WelcomePage = WelcomePage;\n_WelcomePage.ɵfac = function WelcomePage_Factory(t) {\n  return new (t || _WelcomePage)(i0.ɵɵdirectiveInject(i1.NavController));\n};\n_WelcomePage.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: _WelcomePage,\n  selectors: [[\"app-welcome\"]],\n  decls: 18,\n  vars: 0,\n  consts: [[1, \"welcome-wrapper\"], [1, \"main-content\"], [\"src\", \"/assets/icon-welcome.svg\", 1, \"welcome-icon\"], [1, \"welcome-text\"], [1, \"platform-section\"], [1, \"platform-buttons\"], [1, \"platform-button\", 3, \"click\"], [\"src\", \"assets/onboarding_images/winpluspharm.svg\", \"alt\", \"WinPlus Pharma\", 1, \"platform-image\"], [\"src\", \"assets/onboarding_images/winpharm.svg\", \"alt\", \"Pharmalier\", 1, \"platform-image\"]],\n  template: function WelcomePage_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelement(0, \"ion-header\");\n      i0.ɵɵelementStart(1, \"ion-content\")(2, \"div\", 0)(3, \"div\", 1);\n      i0.ɵɵelement(4, \"img\", 2);\n      i0.ɵɵelementStart(5, \"div\", 3)(6, \"h2\");\n      i0.ɵɵtext(7, \"Scannez, r\\u00E9cup\\u00E9rez, automatisez !\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(8, \"p\");\n      i0.ɵɵtext(9, \"Vos bons de livraison enregistr\\u00E9s automatiquement en un instant.\");\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(10, \"div\", 4)(11, \"h3\");\n      i0.ɵɵtext(12, \"Choisir votre plateforme\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(13, \"div\", 5)(14, \"div\", 6);\n      i0.ɵɵlistener(\"click\", function WelcomePage_Template_div_click_14_listener() {\n        return ctx.selectPlatform(\"winpluspharma\");\n      });\n      i0.ɵɵelement(15, \"img\", 7);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(16, \"div\", 6);\n      i0.ɵɵlistener(\"click\", function WelcomePage_Template_div_click_16_listener() {\n        return ctx.selectPlatform(\"pharmalier\");\n      });\n      i0.ɵɵelement(17, \"img\", 8);\n      i0.ɵɵelementEnd()()()()();\n    }\n  },\n  dependencies: [i1.IonContent, i1.IonHeader],\n  styles: [\"@import url(https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap);@import url(https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0[_ngcontent-%COMP%], 200[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 300[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 400[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 500[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 600[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 700[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 800[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 900[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 100[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 200[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 300[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 400[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 500[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 600[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 700[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 800[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 900&display=swap)[_ngcontent-%COMP%];*[_ngcontent-%COMP%] {\\n  font-family: \\\"Inter\\\", sans-serif;\\n  font-optical-sizing: auto;\\n}\\n\\nion-content[_ngcontent-%COMP%]::part(scroll) {\\n  overflow-y: auto;\\n}\\n\\nion-button[_ngcontent-%COMP%]::part(native) {\\n  --padding-top: 20px !important;\\n  --padding-bottom: 20px !important;\\n}\\n\\n.welcome-wrapper[_ngcontent-%COMP%] {\\n  background: url(\\\"/assets/bg-welcome.png\\\") no-repeat center center fixed;\\n  background-size: cover;\\n  height: 100%;\\n}\\n\\nion-footer[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n  padding: 10px;\\n  display: flex;\\n  justify-content: center;\\n  flex-direction: row;\\n  align-items: center;\\n}\\n\\nion-toolbar[_ngcontent-%COMP%] {\\n  --background: transparent;\\n  --ion-color-primary: #2f4fcd;\\n}\\n\\nion-button[_ngcontent-%COMP%] {\\n  margin: 10px;\\n}\\n\\nion-button.welcome-button[_ngcontent-%COMP%] {\\n  --background: #1f41bb;\\n  --background-activated: #1f41bb;\\n  --border-radius: 8px;\\n  width: 65%;\\n  text-align: center;\\n  box-shadow: 0px 16px 20px rgb(203, 214, 255); \\n\\n  color: #fff;\\n  font-size: 20px;\\n  font-weight: bold;\\n}\\n\\nion-row[_ngcontent-%COMP%] {\\n  height: 100%;\\n  height: 85vh;\\n}\\n\\n  ion-row ion-col {\\n  padding-bottom: 0 !important;\\n}\\n\\n  ion-col {\\n  display: flex !important;\\n  flex-direction: column;\\n  justify-content: space-evenly;\\n  align-items: center;\\n}\\n\\n  img {\\n  width: auto;\\n  max-width: 100%;\\n  height: auto;\\n  max-height: 100%;\\n}\\n\\n  .content-slide {\\n  text-align: left;\\n  padding: 0 20px;\\n}\\n\\n  .content-slide h2 {\\n  font-family: \\\"Poppins\\\", \\\"Inter\\\", sans-serif !important;\\n  font-weight: bold;\\n  font-style: normal;\\n  font-size: 30px;\\n  color: #1f41bb;\\n}\\n\\n  .content-slide p {\\n  padding-right: 20px;\\n  margin-top: 40px;\\n  letter-spacing: 1.1px;\\n}\\n\\n.welcome-wrapper[_ngcontent-%COMP%] {\\n  height: 100vh;\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: center;\\n  align-items: center;\\n  padding: 20px;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n}\\n\\n.main-content[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 40px;\\n  color: white;\\n}\\n.main-content[_ngcontent-%COMP%]   .welcome-icon[_ngcontent-%COMP%] {\\n  width: 80px;\\n  height: 80px;\\n  margin-bottom: 20px;\\n}\\n.main-content[_ngcontent-%COMP%]   .welcome-text[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-family: \\\"Poppins\\\", \\\"Inter\\\", sans-serif !important;\\n  font-weight: 700;\\n  font-size: 1.8rem;\\n  margin-bottom: 10px;\\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\\n}\\n.main-content[_ngcontent-%COMP%]   .welcome-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  opacity: 0.9;\\n  margin: 0;\\n  line-height: 1.4;\\n}\\n\\n.platform-section[_ngcontent-%COMP%] {\\n  text-align: center;\\n  width: 100%;\\n  max-width: 300px;\\n}\\n.platform-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-family: \\\"Poppins\\\", \\\"Inter\\\", sans-serif !important;\\n  font-weight: 600;\\n  font-size: 1.2rem;\\n  color: white;\\n  margin-bottom: 20px;\\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\\n}\\n\\n.platform-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  gap: 20px;\\n}\\n\\n.platform-button[_ngcontent-%COMP%] {\\n  flex: 1;\\n  background: white;\\n  border-radius: 16px;\\n  padding: 20px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\\n  border: 2px solid transparent;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n}\\n.platform-button[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);\\n  border-color: #667eea;\\n}\\n.platform-button[_ngcontent-%COMP%]:active {\\n  transform: translateY(0);\\n}\\n.platform-button[_ngcontent-%COMP%]   .platform-image[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  object-fit: contain;\\n}\\n\\n.platform-image-container[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n  margin-right: 15px;\\n}\\n.platform-image-container[_ngcontent-%COMP%]   .platform-image[_ngcontent-%COMP%] {\\n  width: 50px;\\n  height: 50px;\\n  object-fit: contain;\\n}\\n\\n.platform-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.platform-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  font-family: \\\"Poppins\\\", \\\"Inter\\\", sans-serif !important;\\n  font-weight: 600;\\n  font-size: 18px;\\n  color: #1f41bb;\\n  margin: 0 0 5px 0;\\n}\\n.platform-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #666;\\n  margin: 0;\\n  line-height: 1.4;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n});", "map": {"version": 3, "names": ["WelcomePage", "constructor", "navCtrl", "ngOnInit", "console", "log", "selectPlatform", "platform", "localStorage", "setItem", "navigateForward", "queryParams", "i0", "ɵɵdirectiveInject", "i1", "NavController", "selectors", "decls", "vars", "consts", "template", "WelcomePage_Template", "rf", "ctx", "ɵɵelement", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "WelcomePage_Template_div_click_14_listener", "WelcomePage_Template_div_click_16_listener"], "sources": ["C:\\Users\\<USER>\\Downloads\\Work __<PERSON><PERSON><PERSON><PERSON><PERSON>_ouhna\\OCR_DOCUMENT_GROSSISTE\\Frontend ocr grossiste document\\frontend_ocr_grossiste_document\\src\\app\\welcome\\welcome.page.ts", "C:\\Users\\<USER>\\Downloads\\Work __<PERSON><PERSON><PERSON><PERSON><PERSON>_ouhna\\OCR_DOCUMENT_GROSSISTE\\Frontend ocr grossiste document\\frontend_ocr_grossiste_document\\src\\app\\welcome\\welcome.page.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { NavController } from '@ionic/angular';\r\n\r\n@Component({\r\n  selector: 'app-welcome',\r\n  templateUrl: './welcome.page.html',\r\n  styleUrls: ['./welcome.page.scss'],\r\n})\r\nexport class WelcomePage implements OnInit {\r\n\r\n  constructor(private navCtrl: NavController) {}\r\n\r\n  ngOnInit() {\r\n    console.log(\"Welcome Page\");\r\n  }\r\n\r\n  selectPlatform(platform: 'winpluspharma' | 'pharmalier') {\r\n    // Store the selected platform\r\n    localStorage.setItem('src_app', platform);\r\n\r\n    // Navigate to login with platform parameter\r\n    this.navCtrl.navigateForward('/login', {\r\n      queryParams: { platform: platform }\r\n    });\r\n  }\r\n}\r\n", "<ion-header>\r\n\r\n</ion-header>\r\n\r\n<ion-content>\r\n  <div class=\"welcome-wrapper\">\r\n    <!-- Main Content -->\r\n    <div class=\"main-content\">\r\n      <img src=\"/assets/icon-welcome.svg\" class=\"welcome-icon\" />\r\n      <div class=\"welcome-text\">\r\n        <h2><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, automatisez !</h2>\r\n        <p>Vos bons de livraison enregistrés automatiquement en un instant.</p>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Platform Selection -->\r\n    <div class=\"platform-section\">\r\n      <h3>Choisir votre plateforme</h3>\r\n\r\n      <div class=\"platform-buttons\">\r\n        <div class=\"platform-button\" (click)=\"selectPlatform('winpluspharma')\">\r\n          <img src=\"assets/onboarding_images/winpluspharm.svg\" alt=\"WinPlus Pharma\" class=\"platform-image\">\r\n        </div>\r\n\r\n        <div class=\"platform-button\" (click)=\"selectPlatform('pharmalier')\">\r\n          <img src=\"assets/onboarding_images/winpharm.svg\" alt=\"Pharmalier\" class=\"platform-image\">\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</ion-content>"], "mappings": ";;;AAQA,OAAM,MAAOA,WAAW;EAEtBC,YAAoBC,OAAsB;IAAtB,KAAAA,OAAO,GAAPA,OAAO;EAAkB;EAE7CC,QAAQA,CAAA;IACNC,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;EAC7B;EAEAC,cAAcA,CAACC,QAAwC;IACrD;IACAC,YAAY,CAACC,OAAO,CAAC,SAAS,EAAEF,QAAQ,CAAC;IAEzC;IACA,IAAI,CAACL,OAAO,CAACQ,eAAe,CAAC,QAAQ,EAAE;MACrCC,WAAW,EAAE;QAAEJ,QAAQ,EAAEA;MAAQ;KAClC,CAAC;EACJ;;eAhBWP,WAAW;;mBAAXA,YAAW,EAAAY,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,aAAA;AAAA;;QAAXf,YAAW;EAAAgB,SAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,qBAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MCRxBV,EAAA,CAAAY,SAAA,iBAEa;MAKTZ,EAHJ,CAAAa,cAAA,kBAAa,aACkB,aAED;MACxBb,EAAA,CAAAY,SAAA,aAA2D;MAEzDZ,EADF,CAAAa,cAAA,aAA0B,SACpB;MAAAb,EAAA,CAAAc,MAAA,kDAAiC;MAAAd,EAAA,CAAAe,YAAA,EAAK;MAC1Cf,EAAA,CAAAa,cAAA,QAAG;MAAAb,EAAA,CAAAc,MAAA,4EAAgE;MAEvEd,EAFuE,CAAAe,YAAA,EAAI,EACnE,EACF;MAIJf,EADF,CAAAa,cAAA,cAA8B,UACxB;MAAAb,EAAA,CAAAc,MAAA,gCAAwB;MAAAd,EAAA,CAAAe,YAAA,EAAK;MAG/Bf,EADF,CAAAa,cAAA,cAA8B,cAC2C;MAA1Cb,EAAA,CAAAgB,UAAA,mBAAAC,2CAAA;QAAA,OAASN,GAAA,CAAAjB,cAAA,CAAe,eAAe,CAAC;MAAA,EAAC;MACpEM,EAAA,CAAAY,SAAA,cAAiG;MACnGZ,EAAA,CAAAe,YAAA,EAAM;MAENf,EAAA,CAAAa,cAAA,cAAoE;MAAvCb,EAAA,CAAAgB,UAAA,mBAAAE,2CAAA;QAAA,OAASP,GAAA,CAAAjB,cAAA,CAAe,YAAY,CAAC;MAAA,EAAC;MACjEM,EAAA,CAAAY,SAAA,cAAyF;MAKnGZ,EAJQ,CAAAe,YAAA,EAAM,EACF,EACF,EACF,EACM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}