{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Downloads/Work __Abderrahmane_ouhna/OCR_DOCUMENT_GROSSISTE/Frontend ocr grossiste document/frontend_ocr_grossiste_document/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nvar _AppComponent;\nimport { register } from 'swiper/element/bundle';\nimport { NavigationStart } from '@angular/router';\nimport { environment } from '../environments/environment'; // Import the environment\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./services/storage.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"./services/api.service\";\nimport * as i4 from \"@ionic/angular\";\nimport * as i5 from \"@angular/common\";\nconst _c0 = a0 => ({\n  \"web-environment\": a0\n});\nregister();\nexport class AppComponent {\n  constructor(storageService, router, apiService, platform) {\n    this.storageService = storageService;\n    this.router = router;\n    this.apiService = apiService;\n    this.platform = platform;\n    // constructor(private networkService: NetworkService, private navCtrl: NavController) {\n    //   this.networkService.getNetworkStatus().subscribe((connected: boolean) => {\n    //     if (!connected) {\n    //       this.navCtrl.navigateRoot('/network-error');\n    //     }\n    //   });\n    // }\n    this.environment = environment;\n    this.initializeApp();\n  }\n  initializeApp() {\n    this.platform.ready().then(() => {\n      this.forceLightMode();\n      this.checkEnvironmentAndClearStorage();\n    });\n  }\n  forceLightMode() {\n    // Remove dark mode from body\n    document.body.classList.remove('dark');\n    document.documentElement.classList.remove('dark');\n    // Add light mode\n    document.body.classList.add('light');\n    document.documentElement.classList.add('light');\n    // Set attribute\n    document.body.setAttribute('data-theme', 'light');\n    // Force color scheme\n    const meta = document.createElement('meta');\n    meta.name = 'color-scheme';\n    meta.content = 'light';\n    document.head.appendChild(meta);\n  }\n  ngOnInit() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      // Ensure light mode is applied\n      _this.forceLightMode();\n      // Initialize storage\n      yield _this.storageService.init();\n      // Check if the user has seen onboarding\n      const hasSeenOnboarding = yield _this.storageService.get('hasSeenOnboarding');\n      if (!hasSeenOnboarding) {\n        // Navigate to the onboarding screen if not seen before\n        _this.router.navigate(['/onboarding']);\n      }\n      // Handle route redirection for logged-in users\n      _this.router.events.subscribe(event => {\n        if (event instanceof NavigationStart) {\n          if (!_this.apiService.isLoggedIn() && !_this.isPublicRoute(event.url)) {\n            _this.router.navigate(['/welcome']);\n          }\n        }\n      });\n    })();\n  }\n  isPublicRoute(url) {\n    const publicRoutes = ['/login', '/onboarding', '/welcome', '/network-error', '/request-error'];\n    return publicRoutes.includes(url);\n  }\n  checkEnvironmentAndClearStorage() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      if (!environment.production) {\n        console.log('Non-production environment detected. Clearing storage...');\n        yield _this2.storageService.clear(); // Clear all storage\n      }\n    })();\n  }\n}\n_AppComponent = AppComponent;\n_AppComponent.ɵfac = function AppComponent_Factory(t) {\n  return new (t || _AppComponent)(i0.ɵɵdirectiveInject(i1.StorageService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.ApiService), i0.ɵɵdirectiveInject(i4.Platform));\n};\n_AppComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: _AppComponent,\n  selectors: [[\"app-root\"]],\n  decls: 2,\n  vars: 3,\n  consts: [[3, \"ngClass\"]],\n  template: function AppComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"ion-app\", 0);\n      i0.ɵɵelement(1, \"ion-router-outlet\");\n      i0.ɵɵelementEnd();\n    }\n    if (rf & 2) {\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c0, ctx.environment.platform === \"web\"));\n    }\n  },\n  dependencies: [i5.NgClass, i4.IonApp, i4.IonRouterOutlet],\n  styles: [\".web-environment[_ngcontent-%COMP%] {\\n  max-width: 920px;\\n  margin: auto;\\n  overflow: hidden;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvYXBwLmNvbXBvbmVudC5zY3NzIiwid2VicGFjazovLy4vLi4vLi4vLi4vLi4vV29yayUyMF9fQWJkZXJyYWhtYW5lX291aG5hL09DUl9ET0NVTUVOVF9HUk9TU0lTVEUvRnJvbnRlbmQlMjBvY3IlMjBncm9zc2lzdGUlMjBkb2N1bWVudC9mcm9udGVuZF9vY3JfZ3Jvc3Npc3RlX2RvY3VtZW50L3NyYy9hcHAvYXBwLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0ksZ0JBQUE7RUFDQSxZQUFBO0VBQ0EsZ0JBQUE7QUNDSiIsInNvdXJjZXNDb250ZW50IjpbIi53ZWItZW52aXJvbm1lbnR7XHJcbiAgICBtYXgtd2lkdGg6IDkyMHB4O1xyXG4gICAgbWFyZ2luOiBhdXRvO1xyXG4gICAgb3ZlcmZsb3c6IGhpZGRlbjtcclxufSIsIi53ZWItZW52aXJvbm1lbnQge1xuICBtYXgtd2lkdGg6IDkyMHB4O1xuICBtYXJnaW46IGF1dG87XG4gIG92ZXJmbG93OiBoaWRkZW47XG59Il0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n});", "map": {"version": 3, "names": ["register", "NavigationStart", "environment", "AppComponent", "constructor", "storageService", "router", "apiService", "platform", "initializeApp", "ready", "then", "forceLightMode", "checkEnvironmentAndClearStorage", "document", "body", "classList", "remove", "documentElement", "add", "setAttribute", "meta", "createElement", "name", "content", "head", "append<PERSON><PERSON><PERSON>", "ngOnInit", "_this", "_asyncToGenerator", "init", "hasSeenOnboarding", "get", "navigate", "events", "subscribe", "event", "isLoggedIn", "isPublicRoute", "url", "publicRoutes", "includes", "_this2", "production", "console", "log", "clear", "i0", "ɵɵdirectiveInject", "i1", "StorageService", "i2", "Router", "i3", "ApiService", "i4", "Platform", "selectors", "decls", "vars", "consts", "template", "AppComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵproperty", "ɵɵpureFunction1", "_c0"], "sources": ["C:\\Users\\<USER>\\Downloads\\Work __<PERSON><PERSON><PERSON><PERSON><PERSON>_ouhna\\OCR_DOCUMENT_GROSSISTE\\Frontend ocr grossiste document\\frontend_ocr_grossiste_document\\src\\app\\app.component.ts", "C:\\Users\\<USER>\\Downloads\\Work __<PERSON><PERSON><PERSON><PERSON><PERSON>_ouhna\\OCR_DOCUMENT_GROSSISTE\\Frontend ocr grossiste document\\frontend_ocr_grossiste_document\\src\\app\\app.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { register } from 'swiper/element/bundle';\r\nimport { Router, NavigationStart } from '@angular/router';\r\nimport { ApiService } from './services/api.service';\r\nimport { Platform } from '@ionic/angular';\r\nimport { StorageService } from './services/storage.service';\r\nimport { environment } from '../environments/environment'; // Import the environment\r\n\r\nregister();\r\n\r\n@Component({\r\n  selector: 'app-root',\r\n  templateUrl: 'app.component.html',\r\n  styleUrls: ['app.component.scss'],\r\n})\r\nexport class AppComponent implements OnInit {\r\n  \r\n  // constructor(private networkService: NetworkService, private navCtrl: NavController) {\r\n  //   this.networkService.getNetworkStatus().subscribe((connected: boolean) => {\r\n  //     if (!connected) {\r\n  //       this.navCtrl.navigateRoot('/network-error');\r\n  //     }\r\n  //   });\r\n  // }\r\n\r\n  environment = environment;\r\n\r\n  \r\n\r\n  constructor(private storageService: StorageService, private router: Router, private apiService: ApiService,private platform: Platform) {\r\n    this.initializeApp();\r\n  }\r\n\r\n  initializeApp() {\r\n    this.platform.ready().then(() => {\r\n      this.forceLightMode();\r\n      this.checkEnvironmentAndClearStorage();\r\n    });\r\n  }\r\n\r\n  private forceLightMode() {\r\n    // Remove dark mode from body\r\n    document.body.classList.remove('dark');\r\n    document.documentElement.classList.remove('dark');\r\n    \r\n    // Add light mode\r\n    document.body.classList.add('light');\r\n    document.documentElement.classList.add('light');\r\n\r\n    // Set attribute\r\n    document.body.setAttribute('data-theme', 'light');\r\n    \r\n    // Force color scheme\r\n    const meta = document.createElement('meta');\r\n    meta.name = 'color-scheme';\r\n    meta.content = 'light';\r\n    document.head.appendChild(meta);\r\n  }\r\n\r\n  async ngOnInit() {\r\n    // Ensure light mode is applied\r\n    this.forceLightMode();\r\n  \r\n    // Initialize storage\r\n    await this.storageService.init();\r\n  \r\n    // Check if the user has seen onboarding\r\n    const hasSeenOnboarding = await this.storageService.get('hasSeenOnboarding');\r\n\r\n\r\n    if (!hasSeenOnboarding) {\r\n      // Navigate to the onboarding screen if not seen before\r\n      this.router.navigate(['/onboarding']);\r\n    }\r\n  \r\n    // Handle route redirection for logged-in users\r\n    this.router.events.subscribe(event => {\r\n      if (event instanceof NavigationStart) {\r\n        if (!this.apiService.isLoggedIn() && !this.isPublicRoute(event.url)) {\r\n          this.router.navigate(['/welcome']);\r\n        }\r\n      }\r\n    });\r\n  }\r\n\r\n  isPublicRoute(url: string): boolean {\r\n    const publicRoutes = ['/login', '/onboarding', '/welcome', '/network-error', '/request-error'];\r\n    return publicRoutes.includes(url);\r\n  }\r\n\r\n\r\n  private async checkEnvironmentAndClearStorage() {\r\n    if (!environment.production) {\r\n      console.log('Non-production environment detected. Clearing storage...');\r\n      await this.storageService.clear(); // Clear all storage\r\n    }\r\n  }\r\n}\r\n", "<ion-app [ngClass]=\"{'web-environment': environment.platform === 'web'}\">\n  <ion-router-outlet></ion-router-outlet>\n</ion-app>\n"], "mappings": ";;AACA,SAASA,QAAQ,QAAQ,uBAAuB;AAChD,SAAiBC,eAAe,QAAQ,iBAAiB;AAIzD,SAASC,WAAW,QAAQ,6BAA6B,CAAC,CAAC;;;;;;;;;;AAE3DF,QAAQ,EAAE;AAOV,OAAM,MAAOG,YAAY;EAcvBC,YAAoBC,cAA8B,EAAUC,MAAc,EAAUC,UAAsB,EAASC,QAAkB;IAAjH,KAAAH,cAAc,GAAdA,cAAc;IAA0B,KAAAC,MAAM,GAANA,MAAM;IAAkB,KAAAC,UAAU,GAAVA,UAAU;IAAqB,KAAAC,QAAQ,GAARA,QAAQ;IAZ3H;IACA;IACA;IACA;IACA;IACA;IACA;IAEA,KAAAN,WAAW,GAAGA,WAAW;IAKvB,IAAI,CAACO,aAAa,EAAE;EACtB;EAEAA,aAAaA,CAAA;IACX,IAAI,CAACD,QAAQ,CAACE,KAAK,EAAE,CAACC,IAAI,CAAC,MAAK;MAC9B,IAAI,CAACC,cAAc,EAAE;MACrB,IAAI,CAACC,+BAA+B,EAAE;IACxC,CAAC,CAAC;EACJ;EAEQD,cAAcA,CAAA;IACpB;IACAE,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACC,MAAM,CAAC,MAAM,CAAC;IACtCH,QAAQ,CAACI,eAAe,CAACF,SAAS,CAACC,MAAM,CAAC,MAAM,CAAC;IAEjD;IACAH,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACG,GAAG,CAAC,OAAO,CAAC;IACpCL,QAAQ,CAACI,eAAe,CAACF,SAAS,CAACG,GAAG,CAAC,OAAO,CAAC;IAE/C;IACAL,QAAQ,CAACC,IAAI,CAACK,YAAY,CAAC,YAAY,EAAE,OAAO,CAAC;IAEjD;IACA,MAAMC,IAAI,GAAGP,QAAQ,CAACQ,aAAa,CAAC,MAAM,CAAC;IAC3CD,IAAI,CAACE,IAAI,GAAG,cAAc;IAC1BF,IAAI,CAACG,OAAO,GAAG,OAAO;IACtBV,QAAQ,CAACW,IAAI,CAACC,WAAW,CAACL,IAAI,CAAC;EACjC;EAEMM,QAAQA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACZ;MACAD,KAAI,CAAChB,cAAc,EAAE;MAErB;MACA,MAAMgB,KAAI,CAACvB,cAAc,CAACyB,IAAI,EAAE;MAEhC;MACA,MAAMC,iBAAiB,SAASH,KAAI,CAACvB,cAAc,CAAC2B,GAAG,CAAC,mBAAmB,CAAC;MAG5E,IAAI,CAACD,iBAAiB,EAAE;QACtB;QACAH,KAAI,CAACtB,MAAM,CAAC2B,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;;MAGvC;MACAL,KAAI,CAACtB,MAAM,CAAC4B,MAAM,CAACC,SAAS,CAACC,KAAK,IAAG;QACnC,IAAIA,KAAK,YAAYnC,eAAe,EAAE;UACpC,IAAI,CAAC2B,KAAI,CAACrB,UAAU,CAAC8B,UAAU,EAAE,IAAI,CAACT,KAAI,CAACU,aAAa,CAACF,KAAK,CAACG,GAAG,CAAC,EAAE;YACnEX,KAAI,CAACtB,MAAM,CAAC2B,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC;;;MAGxC,CAAC,CAAC;IAAC;EACL;EAEAK,aAAaA,CAACC,GAAW;IACvB,MAAMC,YAAY,GAAG,CAAC,QAAQ,EAAE,aAAa,EAAE,UAAU,EAAE,gBAAgB,EAAE,gBAAgB,CAAC;IAC9F,OAAOA,YAAY,CAACC,QAAQ,CAACF,GAAG,CAAC;EACnC;EAGc1B,+BAA+BA,CAAA;IAAA,IAAA6B,MAAA;IAAA,OAAAb,iBAAA;MAC3C,IAAI,CAAC3B,WAAW,CAACyC,UAAU,EAAE;QAC3BC,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC;QACvE,MAAMH,MAAI,CAACrC,cAAc,CAACyC,KAAK,EAAE,CAAC,CAAC;;IACpC;EACH;;gBAjFW3C,YAAY;;mBAAZA,aAAY,EAAA4C,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAAL,EAAA,CAAAC,iBAAA,CAAAK,EAAA,CAAAC,UAAA,GAAAP,EAAA,CAAAC,iBAAA,CAAAO,EAAA,CAAAC,QAAA;AAAA;;QAAZrD,aAAY;EAAAsD,SAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MCfzBhB,EAAA,CAAAkB,cAAA,iBAAyE;MACvElB,EAAA,CAAAmB,SAAA,wBAAuC;MACzCnB,EAAA,CAAAoB,YAAA,EAAU;;;MAFDpB,EAAA,CAAAqB,UAAA,YAAArB,EAAA,CAAAsB,eAAA,IAAAC,GAAA,EAAAN,GAAA,CAAA9D,WAAA,CAAAM,QAAA,YAA+D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}